# Global Traefik configuration
api:
  dashboard: true
  insecure: true  # Set to false and configure proper security in production

entryPoints:
  web:
    address: ":80"
  websecure:
    address: ":443"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: traefik-network
  file:
    directory: "/etc/traefik"
    watch: true

# Define the host rules for your services
http:
  middlewares:
    simple-auth:
      digestAuth:
        usersFile: "/etc/traefik/.htdigest"
        realm: "traefik"

  routers:
    n8n:
      rule: "Host(`n8n.alanshum.org`)"
      service: n8n
      entryPoints:
        - web
        - websecure

    langflow:
      rule: "Host(`langflow.alanshum.org`)"
      service: langflow
      entryPoints:
        - web
        - websecure
      # middlewares:
      #  - simple-auth

    portainer:
      rule: "Host(`portainer.alanshum.org`)"
      service: portainer
      entryPoints:
        - web
        - websecure

    api-server:
      rule: "Host(`api-server.alanshum.org`)"
      service: api-server
      entryPoints:
        - web
        - websecure

    notion-mcp-server:
      rule: "Host(`notion-mcp.alanshum.org`)"
      service: notion-mcp-server
      entryPoints:
        - web
        - websecure

  services:
    n8n:
      loadBalancer:
        servers:
          - url: "http://n8n:5678"

    langflow:
      loadBalancer:
        servers:
          - url: "http://langflow:7860"

    portainer:
      loadBalancer:
        servers:
          - url: "http://portainer:9000"

    api-server:
      loadBalancer:
        servers:
          - url: "http://api-server:3001"

    notion-mcp-server:
      loadBalancer:
        servers:
          - url: "http://notion-mcp-server:3002"
