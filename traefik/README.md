# Traefik Reverse Proxy Setup with Cloudflare Tunnel

This directory contains the configuration for T<PERSON><PERSON><PERSON>, which serves as a reverse proxy for the services in this repository. The setup works with Cloudflare Tunnel for external access and SSL termination.

## Configuration

- Traefik is configured to route traffic to the following services:
  - n8n: accessible at https://n8n.alanshum.org
  - Langflow: accessible at https://langflow.alanshum.org
  - Traefik Dashboard: accessible at https://traefik.alanshum.org
  - Portainer: accessible at https://portainer.alanshum.org

## Architecture

```
External Request → Cloudflare Tunnel → Traefik Reverse Proxy → Service (n8n/langflow)
```

## Usage

To use this setup:

1. Make sure your Cloudflare Tunnel is configured to forward traffic to this server.

2. Start the Traefik service first:
   ```bash
   docker-compose up -d
   ```

3. Then start the individual services:
   ```bash
   cd n8n && docker-compose up -d
   cd langflow && docker-compose up -d
   cd portainer && docker-compose up -d
   ```

## Security Notes

- The Traefik dashboard is accessible at https://traefik.alanshum.org
- In production, consider adding authentication to the Traefik dashboard
- SSL termination is handled by Cloudflare, so internal communication between Cloudflare Tunnel and Traefik can be unencrypted
