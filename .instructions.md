# Automation Agent - GitHub Copilot Instructions

## Project Overview

This is a microservices-based automation platform built with Docker and containerized services. The project includes workflow automation tools (n8n, Langflow), container management (Portainer), reverse proxy (Traefik), APIs, and supporting services like Redis and Notion MCP server.

## Programming Languages & Frameworks

### Primary Stack
- **TypeScript/Node.js**: Main API development (api-server)
- **Docker/Docker Compose**: Containerization and orchestration
- **YAML**: Configuration files (docker-compose.yml, traefik.yml)
- **Shell Scripts**: Automation and service management
- **Fastify**: Web framework for API server with plugins for OAuth, sessions, cookies

### Service Stack
- **Traefik v2.10**: Reverse proxy and load balancer
- **PostgreSQL**: Database for Langflow
- **Redis**: Caching and queue management for n8n
- **n8n**: Workflow automation
- **Langflow**: LangChain flow editor
- **Portainer**: Docker management UI

## Project Structure & Organization

### Directory Structure
```
automation-agent/
├── README.md                 # Main project documentation
├── start-all-services.sh     # Service startup orchestration
├── stop-all-services.sh      # Service shutdown orchestration
├── {service-name}/           # Each service in its own directory
│   ├── docker-compose.yml    # Service-specific Docker configuration
│   ├── Dockerfile           # Custom image builds (when needed)
│   ├── README.md            # Service-specific documentation
│   └── config/              # Service configuration files

```

### Service Naming Convention
- **kebab-case** for directory names (`api-server`, `notion-mcp-server`)
- **kebab-case** for Docker service names
- **kebab-case** for Docker network names (`traefik-network`)
- **snake_case** for Docker volume names (`portainer_data`, `redis-data`)

## Coding Standards & Conventions

### TypeScript/Node.js (api-server)
- **Strict TypeScript**: `"strict": true` in tsconfig.json
- **ES2020 target** with CommonJS modules
- **Fastify framework** with typed plugins
- **Environment-based configuration** using `@fastify/env`
- **Interface definitions** for type safety (e.g., `OAuthUser`, `ConfigSchema`)
- **Async/await** pattern for asynchronous operations
- **Modular routing** with separate route files

### Docker Standards
- **Multi-stage builds** when applicable
- **External networks** for service communication (`traefik-network`)
- **Named volumes** for data persistence
- **Environment variables** for configuration
- **Health checks** for critical services
- **Restart policies**: `unless-stopped` for production services

### Configuration Management
- **Environment variables** for sensitive data (`.env` files)
- **YAML configuration** for Docker Compose and Traefik
- **JSON configuration** for Node.js projects (package.json, tsconfig.json)
- **Example files** provided for sensitive configurations (`.env.example`)

## File Naming Conventions

### Configuration Files
- `docker-compose.yml` (not `docker-compose.yaml`)
- `.env` for environment variables
- `.env.example` for environment templates
- `tsconfig.json` for TypeScript configuration
- `nodemon.json` for development configuration

### Documentation
- `README.md` for each service and main project
- `docs/` subdirectory for additional documentation
- Inline comments in configuration files using `#` for YAML and `//` for JSON

### Scripts
- `kebab-case` for script names (`start-all-services.sh`)
- `.sh` extension for shell scripts
- Executable permissions for all scripts

## Architectural Patterns

### Microservices Architecture
- **Service isolation**: Each service runs in its own container
- **Shared networking**: All services connected via `traefik-network`
- **Centralized routing**: Traefik handles all external traffic routing
- **Service discovery**: Docker service names used for internal communication

### Infrastructure as Code
- **Declarative configuration**: All services defined in docker-compose.yml
- **Version controlled**: All configuration files tracked in Git
- **Environment separation**: Development configurations with live reloading
- **Orchestration scripts**: Automated startup/shutdown sequences

### Security Patterns
- **OAuth 2.0 integration**: Google OAuth for authentication
- **Session management**: Secure session cookies with proper configuration
- **Network isolation**: Services communicate through defined networks only
- **Secrets management**: Environment variables for sensitive data
- **Reverse proxy security**: Traefik handles SSL termination via Cloudflare

## Development Practices

### Development Environment
- **Live reloading**: Development containers mount source code for hot reloading
- **Debug support**: Port 9229 exposed for Node.js debugging
- **Local development**: Services accessible on localhost with port mapping
- **Volume mounting**: Source code mounted for development (`./:/app`)

### Error Handling
- **Graceful error handling**: Try-catch blocks in async functions
- **HTTP error responses**: Proper status codes and error messages
- **Logging**: Fastify built-in logger for request/error logging
- **Health checks**: Docker health checks for service monitoring

### Testing Approach
- **Development focus**: Current setup optimized for development
- **Integration testing**: Services can be tested as integrated system
- **Docker testing**: Test services in containerized environment

## Git & Version Control

### Branch Strategy
- **Feature development**: Implied feature branch workflow
- **Main branch**: Production-ready code

### Commit Guidelines
Based on the project structure, follow conventional commits:
- `feat:` for new features or services
- `fix:` for bug fixes
- `docs:` for documentation updates
- `config:` for configuration changes
- `docker:` for Docker-related changes
- `refactor:` for code refactoring

### Security Considerations
- **Environment files**: `.env` files are gitignored
- **Example configurations**: `.env.example` provided for setup guidance
- **Secrets**: Authentication files like `.htdigest` are gitignored

## Service Communication

### Internal Communication
- **Service names**: Use Docker service names for internal requests (e.g., `redis`, `postgres`)
- **Shared networks**: All services on `traefik-network` for discovery
- **Port standards**: Standard ports for each service type

### External Access
- **Domain routing**: Services accessible via subdomain routing
- **SSL termination**: Handled by Cloudflare Tunnel + Traefik
- **Development access**: Local ports exposed for development

## Best Practices

### Configuration
- Always provide `.env.example` files for services requiring environment variables
- Use typed configuration schemas (TypeScript interfaces)
- Validate required environment variables at startup
- Document all configuration options in README files

### Docker
- Use specific version tags for production images
- Implement proper health checks for all services
- Use named volumes for data that needs to persist
- Keep Dockerfiles optimized and use multi-stage builds when beneficial

### Documentation
- Maintain comprehensive README.md for each service
- Include setup instructions and examples
- Document API endpoints and authentication flows
- Provide troubleshooting guidance

### Security
- Never commit sensitive data (use .gitignore)
- Use environment variables for configuration
- Implement proper authentication and session management
- Follow principle of least privilege for container permissions

### Monitoring & Operations
- Use proper restart policies (`unless-stopped`)
- Implement health checks where applicable
- Provide orchestration scripts for common operations
- Structure logging for easy debugging and monitoring

## Domain Configuration

The project uses `alanshum.org` domain with service-specific subdomains:

- `n8n.alanshum.org` - n8n workflow automation
- `langflow.alanshum.org` - Langflow interface
- `portainer.alanshum.org` - Container management
- `api-server.alanshum.org` - Main API server

When adding new services, follow this subdomain pattern and update the ingress configuration accordingly.
