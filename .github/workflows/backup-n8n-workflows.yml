name: Backup n8n Workflows

on:
  # Run daily at 2 AM UTC
  schedule:
    - cron: '0 2 * * *'

  # Allow manual triggering
  workflow_dispatch:
    inputs:
      backup_type:
        description: 'Backup type'
        required: false
        default: 'manual'
        type: choice
        options:
          - manual
          - daily
      cleanup_days:
        description: 'Days to keep backups'
        required: false
        default: '30'
        type: string

jobs:
  backup:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        # Fetch full history for proper git operations
        fetch-depth: 0
        # Use a personal access token for pushing
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Set up environment
      run: |
        # Install jq for JSON processing
        sudo apt-get update
        sudo apt-get install -y jq

        # Make backup script executable
        chmod +x scripts/n8n-workflows/backup-n8n-workflows.sh

        # Update CA certificates for SSL
        sudo apt-get install -y ca-certificates

    - name: Configure Git
      run: |
        git config --global user.name "GitHub Actions Bot"
        git config --global user.email "<EMAIL>"

    - name: Run n8n workflow backup
      env:
        N8N_API_KEY: ${{ secrets.N8N_API_KEY }}
        N8N_HOST: ${{ secrets.N8N_HOST || 'https://n8n.alanshum.org' }}
      run: |
        # Determine backup type
        if [ "${{ github.event_name }}" = "schedule" ]; then
          BACKUP_TYPE="daily"
        else
          BACKUP_TYPE="${{ github.event.inputs.backup_type || 'manual' }}"
        fi

        # Determine cleanup days
        CLEANUP_DAYS="${{ github.event.inputs.cleanup_days || '30' }}"

        echo "Running backup with type: $BACKUP_TYPE, cleanup: $CLEANUP_DAYS days"

        # Run the backup script
        ./scripts/n8n-workflows/backup-n8n-workflows.sh \
          --type "$BACKUP_TYPE" \
          --cleanup "$CLEANUP_DAYS"

    - name: Push changes
      run: |
        # Check if there are any changes to push
        if git diff --quiet HEAD~1 HEAD; then
          echo "No changes to push"
        else
          echo "Pushing backup changes to repository"
          git push origin main
        fi

    - name: Create backup summary
      if: always()
      run: |
        echo "## Backup Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        # Get latest backup info
        LATEST_EXPORT=$(find n8n-workflows/exports -name "backup_info.json" -type f | sort | tail -1)

        if [ -f "$LATEST_EXPORT" ]; then
          echo "### Latest Backup Details" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Field | Value |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|-------|" >> $GITHUB_STEP_SUMMARY

          # Extract backup info using jq
          TIMESTAMP=$(jq -r '.timestamp' "$LATEST_EXPORT")
          BACKUP_TYPE=$(jq -r '.backup_type' "$LATEST_EXPORT")
          TOTAL_WORKFLOWS=$(jq -r '.total_workflows' "$LATEST_EXPORT")
          EXPORTED_WORKFLOWS=$(jq -r '.exported_workflows' "$LATEST_EXPORT")
          EXPORT_DATE=$(jq -r '.export_date' "$LATEST_EXPORT")

          echo "| Timestamp | $TIMESTAMP |" >> $GITHUB_STEP_SUMMARY
          echo "| Backup Type | $BACKUP_TYPE |" >> $GITHUB_STEP_SUMMARY
          echo "| Total Workflows | $TOTAL_WORKFLOWS |" >> $GITHUB_STEP_SUMMARY
          echo "| Exported Workflows | $EXPORTED_WORKFLOWS |" >> $GITHUB_STEP_SUMMARY
          echo "| Export Date | $EXPORT_DATE |" >> $GITHUB_STEP_SUMMARY

          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "$EXPORTED_WORKFLOWS" -eq "$TOTAL_WORKFLOWS" ]; then
            echo "✅ **All workflows exported successfully!**" >> $GITHUB_STEP_SUMMARY
          else
            echo "⚠️ **Warning: Not all workflows were exported**" >> $GITHUB_STEP_SUMMARY
          fi
        else
          echo "❌ **No backup information found**" >> $GITHUB_STEP_SUMMARY
        fi

        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Repository Status" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        # Count total backups
        DAILY_BACKUPS=$(find n8n-workflows/backups/daily -name "*.tar.gz" 2>/dev/null | wc -l)
        MANUAL_BACKUPS=$(find n8n-workflows/backups/manual -name "*.tar.gz" 2>/dev/null | wc -l)
        EXPORT_DIRS=$(find n8n-workflows/exports -mindepth 1 -maxdepth 1 -type d 2>/dev/null | wc -l)

        echo "| Type | Count |" >> $GITHUB_STEP_SUMMARY
        echo "|------|-------|" >> $GITHUB_STEP_SUMMARY
        echo "| Daily Backups | $DAILY_BACKUPS |" >> $GITHUB_STEP_SUMMARY
        echo "| Manual Backups | $MANUAL_BACKUPS |" >> $GITHUB_STEP_SUMMARY
        echo "| Export Directories | $EXPORT_DIRS |" >> $GITHUB_STEP_SUMMARY

    - name: Notify on failure
      if: failure()
      run: |
        echo "## ❌ Backup Failed" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "The n8n workflow backup process failed. Please check the logs above for details." >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Common Issues:" >> $GITHUB_STEP_SUMMARY
        echo "- Check if N8N_API_KEY secret is set correctly" >> $GITHUB_STEP_SUMMARY
        echo "- Verify N8N_HOST is accessible" >> $GITHUB_STEP_SUMMARY
        echo "- Ensure n8n instance is running and responsive" >> $GITHUB_STEP_SUMMARY
