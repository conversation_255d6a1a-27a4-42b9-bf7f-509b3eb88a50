# Portainer

This directory contains the Docker Compose configuration for Portainer, a web-based Docker management UI.

## Features

- Web-based Docker container management
- Auto-discovery of running containers
- Container logs and stats monitoring
- Easy container creation and management
- Volume and network management

## Usage

To start Portainer:

```bash
docker-compose up -d
```

Access Portainer at: https://portainer.persoack.org

## First-time Setup

When you first access Portainer, you'll need to:

1. Create an admin user with a secure password
2. Select the "Docker" environment (it should auto-detect your local Docker environment)

## Integration with Traefik

Portainer is configured to work with Traefik as a reverse proxy. The configuration includes:

- Connection to the shared traefik-network
- Labels for Traefik routing
- Host rule for portainer.persoack.org

## Data Persistence

Portainer data is stored in a Docker volume named `portainer_data` to ensure configuration persists across container restarts.
