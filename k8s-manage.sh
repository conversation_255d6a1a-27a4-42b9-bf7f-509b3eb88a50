#!/bin/bash

# Kubernetes Management Script for Automation Services
# Replaces the old Docker Compose start/stop scripts

set -e

NAMESPACE="automation"
MANIFESTS_DIR="k8s-manifests"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if kubectl is available and connected
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed or not in PATH"
        exit 1
    fi

    if ! kubectl cluster-info &> /dev/null; then
        print_error "kubectl is not connected to a cluster"
        exit 1
    fi

    print_status "Connected to cluster: $(kubectl config current-context)"
}

# Function to deploy all services
deploy() {
    print_status "Deploying all services to Kubernetes..."

    # Apply in order: namespace, storage, secrets, configmaps, databases, services, ingress
    kubectl apply -f ${MANIFESTS_DIR}/namespaces/
    kubectl apply -f ${MANIFESTS_DIR}/storage/
    kubectl apply -f ${MANIFESTS_DIR}/secrets/
    kubectl apply -f ${MANIFESTS_DIR}/configmaps/
    kubectl apply -f ${MANIFESTS_DIR}/databases/
    kubectl apply -f ${MANIFESTS_DIR}/services/
    kubectl apply -f ${MANIFESTS_DIR}/ingress/
    kubectl apply -f ${MANIFESTS_DIR}/ssl/

    print_success "All manifests applied successfully!"
    print_status "Waiting for deployments to be ready..."

    # Wait for deployments to be ready
    kubectl wait --for=condition=available --timeout=300s deployment --all -n ${NAMESPACE}

    print_success "All services are running!"
    status
}

# Function to stop all services
stop() {
    print_status "Stopping all services..."

    # Delete in reverse order
    kubectl delete -f ${MANIFESTS_DIR}/services/ --ignore-not-found=true
    kubectl delete -f ${MANIFESTS_DIR}/databases/ --ignore-not-found=true

    print_success "All services stopped!"
}

# Function to restart all services
restart() {
    print_status "Restarting all services..."
    kubectl rollout restart deployment --all -n ${NAMESPACE}

    print_status "Waiting for deployments to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment --all -n ${NAMESPACE}

    print_success "All services restarted!"
    status
}

# Function to show status
status() {
    print_status "Current status of all services:"
    echo
    kubectl get pods -n ${NAMESPACE}
    echo
    print_status "Service endpoints:"
    kubectl get services -n ${NAMESPACE}
    echo
    print_status "Ingress status:"
    kubectl get ingress -n ${NAMESPACE}
    echo
    print_status "SSL Certificates:"
    kubectl get certificates -n ${NAMESPACE}
    echo
    print_status "Monitoring status:"
    kubectl get pods -n monitoring
    echo
    print_status "Access URLs:"
    echo "- n8n: https://n8n.alanshum.org"
    echo "- Langflow: https://langflow.alanshum.org"
    echo "- Portainer: https://portainer.alanshum.org"
    echo "- API Server: https://api-server.alanshum.org"
    echo "- Grafana: https://grafana.alanshum.org (admin/prom-operator)"
    echo "- Prometheus: https://prometheus.alanshum.org"
}

# Function to show logs
logs() {
    local service=$1
    if [ -z "$service" ]; then
        print_error "Please specify a service name"
        print_status "Available services: api-server, langflow, n8n, portainer, postgres-n8n, postgres-langflow, redis"
        exit 1
    fi

    print_status "Showing logs for $service..."
    kubectl logs -f deployment/$service -n ${NAMESPACE}
}

# Function to scale services
scale() {
    local replicas=$1
    if [ -z "$replicas" ]; then
        print_error "Please specify number of replicas"
        exit 1
    fi

    print_status "Scaling all deployments to $replicas replicas..."
    kubectl scale deployment --all --replicas=$replicas -n ${NAMESPACE}

    if [ "$replicas" -gt 0 ]; then
        print_status "Waiting for deployments to be ready..."
        kubectl wait --for=condition=available --timeout=300s deployment --all -n ${NAMESPACE}
    fi

    print_success "All services scaled to $replicas replicas!"
}

# Function to check webhook health
check_webhooks() {
    print_status "Checking admission webhook health..."
    if [ -f "./check-webhooks.sh" ]; then
        ./check-webhooks.sh
    else
        print_error "check-webhooks.sh script not found"
        print_status "You can create this script to monitor webhook health"
    fi
}

# Function to show help
help() {
    echo "Kubernetes Management Script for Automation Services"
    echo
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  deploy       Deploy all services to Kubernetes"
    echo "  stop         Stop all application services (keeps databases)"
    echo "  restart      Restart all services"
    echo "  status       Show current status of all services"
    echo "  logs         Show logs for a specific service"
    echo "  scale        Scale all services to specified number of replicas"
    echo "  webhooks     Check admission webhook health"
    echo "  help         Show this help message"
    echo
    echo "Examples:"
    echo "  $0 deploy                    # Deploy all services"
    echo "  $0 status                    # Check status"
    echo "  $0 logs api-server          # View API server logs"
    echo "  $0 scale 0                  # Scale down all services"
    echo "  $0 scale 1                  # Scale up all services"
    echo "  $0 restart                  # Restart all services"
}

# Main script logic
case "${1:-help}" in
    deploy)
        check_kubectl
        deploy
        ;;
    stop)
        check_kubectl
        stop
        ;;
    restart)
        check_kubectl
        restart
        ;;
    status)
        check_kubectl
        status
        ;;
    logs)
        check_kubectl
        logs $2
        ;;
    scale)
        check_kubectl
        scale $2
        ;;
    webhooks)
        check_kubectl
        check_webhooks
        ;;
    help|--help|-h)
        help
        ;;
    *)
        print_error "Unknown command: $1"
        help
        exit 1
        ;;
esac
