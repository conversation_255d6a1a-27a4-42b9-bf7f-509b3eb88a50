# Migration to Google Kubernetes Engine (GKE)

## ✅ **MIGRATION COMPLETED**

**Status**: All services have been successfully migrated to GKE and are running in production.

### Current Management

Use the Kubernetes management script for all operations:

```bash
# Check status
./k8s-manage.sh status

# Restart services
./k8s-manage.sh restart

# View logs
./k8s-manage.sh logs api-server

# Scale services
./k8s-manage.sh scale 0  # Scale down
./k8s-manage.sh scale 1  # Scale up
```

---

## Migration Documentation

This document outlines the step-by-step process that was used to migrate the multi-service Docker Compose architecture to Google Kubernetes Engine (GKE).

## Current Architecture Overview

Your current setup includes:
- **Traefik**: Reverse proxy and load balancer
- **n8n**: Workflow automation platform with worker scaling
- **Langflow**: AI workflow platform
- **API Server**: Custom Node.js API
- **Portainer**: Container management interface
- **PostgreSQL**: Database for n8n and Langflow
- **Redis**: Queue and caching for n8n

## Pre-Migration Prerequisites

### 1. Google Cloud Setup ✅ COMPLETED
```bash
# Install Google Cloud SDK
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Authenticate with Google Cloud
gcloud auth login
gcloud auth application-default login

# Create a new project (or use existing one)
gcloud projects create macmini-home-server --name="MacMini Home Server"

# Set your project
gcloud config set project macmini-home-server

# Enable required APIs
gcloud services enable container.googleapis.com
gcloud services enable compute.googleapis.com
gcloud services enable storage-api.googleapis.com
gcloud services enable cloudresourcemanager.googleapis.com
```

**Status**: ✅ **COMPLETED**
- Google Cloud SDK v529.0.0 installed and configured
- <NAME_EMAIL>
- Project 'macmini-home-server' set as active
- Required APIs enabled

### 2. Install Required Tools ✅ COMPLETED
```bash
# Install kubectl
gcloud components install kubectl

# Install Helm (for package management)
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
# Alternative: brew install helm (used in our setup)

# Verify installations
kubectl version --client
helm version
```

**Status**: ✅ **COMPLETED**
- kubectl v1.32.3 installed via Homebrew
- Helm v3.18.3 installed via Homebrew
- Both tools verified and working

### 3. Create GKE Cluster ✅ COMPLETED
```bash
# Create the cluster (adjust machine type and node count as needed)
gcloud container clusters create automation-cluster \
  --zone=us-central1-a \
  --machine-type=e2-standard-4 \
  --num-nodes=3 \
  --enable-autoscaling \
  --min-nodes=1 \
  --max-nodes=10 \
  --enable-autorepair \
  --enable-autoupgrade \
  --disk-size=50GB

# Get cluster credentials
gcloud container clusters get-credentials automation-cluster --zone=us-central1-a

# Install required auth plugin
gcloud components install gke-gcloud-auth-plugin

# Verify connection
kubectl cluster-info
kubectl get nodes
```

**Status**: ✅ **COMPLETED**
- GKE cluster 'automation-cluster' created successfully in us-central1-a
- Cluster running with 3 e2-standard-4 nodes
- Autoscaling enabled (1-10 nodes)
- kubectl configured and connected to cluster
- gke-gcloud-auth-plugin installed for authentication

## Phase 1: Prepare Container Images

### ✅ 1. Push Custom Images to Google Container Registry
```bash
# Configure Docker for GCR
gcloud auth configure-docker

# Build and push API Server
cd api-server
DOCKER_BUILDKIT=1 docker build --no-cache -t gcr.io/macmini-home-server/api-server:latest .
docker push gcr.io/macmini-home-server/api-server:latest
```

**Status**: ✅ **COMPLETED**
- API Server image: `gcr.io/macmini-home-server/api-server:latest` (digest: sha256:93b6c3e234e7d29e346efaf6e817a3a14910026ede98b4a841a3b0207fdc86bf)

### ✅ 2. Create Container Registry for Future Updates
```bash
# Enable Artifact Registry (recommended over Container Registry)
gcloud services enable artifactregistry.googleapis.com

# Create repository
gcloud artifacts repositories create automation-repo \
  --repository-format=docker \
  --location=us-central1

# Configure Docker authentication for Artifact Registry
gcloud auth configure-docker us-central1-docker.pkg.dev

# Verify repository creation
gcloud artifacts repositories list --location=us-central1
```

**Status**: ✅ **COMPLETED**
- Artifact Registry API enabled
- Repository 'automation-repo' created in us-central1
- Docker authentication configured for us-central1-docker.pkg.dev
- Repository URL: `us-central1-docker.pkg.dev/macmini-home-server/automation-repo`

## ✅ Phase 2: Create Kubernetes Manifests

### ✅ 1. Create Namespace and Basic Structure
```bash
mkdir -p k8s-manifests/{namespaces,secrets,configmaps,storage,databases,services,ingress}
```

### ✅ 2. Create Namespace
```yaml
# k8s-manifests/namespaces/automation.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: automation
```

### ✅ 3. Create Secrets
```yaml
# k8s-manifests/secrets/database-secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: database-secrets
  namespace: automation
type: Opaque
data:
  # Base64 encoded values
  n8n-db-password: bjhu  # 'n8n' base64 encoded
  langflow-db-password: bGFuZ2Zsb3c=  # 'langflow' base64 encoded
  redis-password: dG9rLXNlbGRvbS1UUkVORFktZXZlbnRz  # 'tok-seldom-TRENDY-events' base64 encoded
  n8n-encryption-key: WXJxSHQ1Qy9LdzBuUVlJRld2UC9hWVlnMk9lVDJSdXU=  # base64 encoded
```

### ✅ 4. Create ConfigMaps
```yaml
# k8s-manifests/configmaps/n8n-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: n8n-config
  namespace: automation
data:
  WEBHOOK_URL: "https://n8n.alanshum.org/"
  N8N_PROTOCOL: "https"
  N8N_HOST: "n8n.alanshum.org"
  N8N_PORT: "5678"
  EXECUTIONS_MODE: "queue"
  QUEUE_BULL_REDIS_HOST: "redis-service"
  QUEUE_BULL_REDIS_PORT: "6379"
  QUEUE_BULL_REDIS_DB: "0"
  N8N_REDIS_HOST: "redis-service"
  N8N_REDIS_PORT: "6379"

# Additional ConfigMaps created:
# - k8s-manifests/configmaps/langflow-config.yaml
# - k8s-manifests/configmaps/api-server-config.yaml
# - k8s-manifests/configmaps/n8n-init-sql.yaml
```

### ✅ 5. Create Persistent Storage
```yaml
# k8s-manifests/storage/persistent-volumes.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-n8n-pvc
  namespace: automation
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard-rwo

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-langflow-pvc
  namespace: automation
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard-rwo

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: automation
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard-rwo

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: n8n-data-pvc
  namespace: automation
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard-rwo
```

**Status**: ✅ **COMPLETED**
- Directory structure created successfully
- Namespace 'automation' created and applied
- Database secrets created with base64 encoded values
- ConfigMaps created for all services (n8n, langflow, api-server, n8n-init-sql)
- Persistent Volume Claims created for all required storage
- All manifests successfully applied to Kubernetes cluster

## ✅ Phase 3: Deploy Database Services

### ✅ 1. PostgreSQL for n8n
```yaml
# k8s-manifests/databases/postgres-n8n.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-n8n
  namespace: automation
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres-n8n
  template:
    metadata:
      labels:
        app: postgres-n8n
    spec:
      containers:
      - name: postgres
        image: postgres:15
        env:
        - name: POSTGRES_USER
          value: "n8n"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: n8n-db-password
        - name: POSTGRES_DB
          value: "n8n"
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: init-script
          mountPath: /docker-entrypoint-initdb.d/
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - n8n
            - -d
            - n8n
          initialDelaySeconds: 30
          periodSeconds: 5
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - n8n
            - -d
            - n8n
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-n8n-pvc
      - name: init-script
        configMap:
          name: n8n-init-sql

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-n8n-service
  namespace: automation
spec:
  selector:
    app: postgres-n8n
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP
```

### ✅ 2. PostgreSQL for Langflow
```yaml
# k8s-manifests/databases/postgres-langflow.yaml
# Similar to postgres-n8n but for Langflow database
```

### ✅ 3. Redis Service
```yaml
# k8s-manifests/databases/redis.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: automation
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:alpine
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: redis-password
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 5
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: automation
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP
```

**Status**: ✅ **COMPLETED**
- PostgreSQL for n8n deployed and running (postgres-n8n-service:5432)
- PostgreSQL for Langflow deployed and running (postgres-langflow-service:5432)
- Redis deployed and running (redis-service:6379)
- All database services have persistent storage configured
- Health checks configured for all database services
- Fixed PostgreSQL mount point issue with subPath configuration

## Phase 4: Deploy Application Services ✅ COMPLETED

### 1. n8n Main Service
```yaml
# k8s-manifests/services/n8n.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: n8n
  namespace: automation
spec:
  replicas: 1
  selector:
    matchLabels:
      app: n8n
  template:
    metadata:
      labels:
        app: n8n
    spec:
      containers:
      - name: n8n
        image: n8nio/n8n:latest
        envFrom:
        - configMapRef:
            name: n8n-config
        env:
        - name: N8N_ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: n8n-encryption-key
        - name: QUEUE_BULL_REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: redis-password
        - name: N8N_REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: redis-password
        ports:
        - containerPort: 5678
        volumeMounts:
        - name: n8n-data
          mountPath: /home/<USER>/.n8n
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
      volumes:
      - name: n8n-data
        persistentVolumeClaim:
          claimName: n8n-data-pvc

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: n8n-worker
  namespace: automation
spec:
  replicas: 2  # Start with 2 workers, can be auto-scaled
  selector:
    matchLabels:
      app: n8n-worker
  template:
    metadata:
      labels:
        app: n8n-worker
    spec:
      containers:
      - name: n8n-worker
        image: n8nio/n8n:latest
        command: ["worker"]
        envFrom:
        - configMapRef:
            name: n8n-config
        env:
        - name: N8N_ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: n8n-encryption-key
        - name: QUEUE_BULL_REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: redis-password
        volumeMounts:
        - name: n8n-data
          mountPath: /home/<USER>/.n8n
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: n8n-data
        persistentVolumeClaim:
          claimName: n8n-data-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: n8n-service
  namespace: automation
spec:
  selector:
    app: n8n
  ports:
  - port: 5678
    targetPort: 5678
  type: ClusterIP
```

### ✅ **Phase 4 Status: COMPLETED**

All application services have been successfully deployed and are running:

```bash
# Verify all services are running
kubectl get pods -n automation
# NAME                                 READY   STATUS    RESTARTS   AGE
# api-server-7d6f7f875d-ltnzc          1/1     Running   0          61m
# langflow-859f9bbd4f-2gwrz            1/1     Running   0          72m
# n8n-67485dbf7f-wk25l                 1/1     Running   0          61m
# n8n-worker-6db96b66b-8rphv           1/1     Running   0          61m
# n8n-worker-6db96b66b-qhfpz           1/1     Running   0          61m
# portainer-5477486bf5-p5zzr           1/1     Running   0          61m

# Check services
kubectl get services -n automation
# All services have ClusterIP addresses and are accessible
```

**Completed Tasks:**
- ✅ n8n main service deployed and running
- ✅ n8n worker services deployed (2 replicas)
- ✅ Langflow service deployed and running
- ✅ API Server service deployed and running
- ✅ Portainer service deployed and running
- ✅ All services have proper health checks configured
- ✅ All services connected to their respective databases
- ✅ ConfigMaps applied for all service configurations
- ✅ Services are accessible via ClusterIP within the cluster

## Phase 5: Setup Ingress and Load Balancing ✅ COMPLETED

### 1. Install NGINX Ingress Controller
```bash
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update

helm install ingress-nginx ingress-nginx/ingress-nginx \
  --namespace ingress-nginx \
  --create-namespace \
  --set controller.service.type=LoadBalancer
```

### 2. Create Ingress Resources
```yaml
# k8s-manifests/ingress/main-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: automation-ingress
  namespace: automation
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - n8n.alanshum.org
    - langflow.alanshum.org
    - api-server.alanshum.org
    secretName: automation-tls
  rules:
  - host: n8n.alanshum.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: n8n-service
            port:
              number: 5678
  - host: langflow.alanshum.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: langflow-service
            port:
              number: 7860
  - host: api-server.alanshum.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-server-service
            port:
              number: 3001
```

### ✅ **Phase 5 Status: COMPLETED**

NGINX Ingress Controller has been successfully deployed and configured:

```bash
# Verify ingress controller
kubectl get service --namespace ingress-nginx ingress-nginx-controller
# NAME                       TYPE           CLUSTER-IP      EXTERNAL-IP    PORT(S)
# ingress-nginx-controller   LoadBalancer   *************   ************   80:31262/TCP,443:31368/TCP

# Check ingress resources
kubectl get ingress -n automation
# NAME                 CLASS   HOSTS                                                                        ADDRESS        PORTS   AGE
# automation-ingress   nginx   n8n.alanshum.org,langflow.alanshum.org,api-server.alanshum.org + 1 more...   ************   80      94s

# Test services through ingress
curl -H "Host: api-server.alanshum.org" http://************/health
curl -H "Host: n8n.alanshum.org" http://************/healthz
curl -H "Host: langflow.alanshum.org" http://************/health
```

**Completed Tasks:**
- ✅ NGINX Ingress Controller installed via Helm
- ✅ LoadBalancer service created with external IP: `************`
- ✅ Ingress resource created for all services
- ✅ All services accessible through ingress (n8n, Langflow, API Server, Portainer)
- ✅ Health checks verified through ingress
- ✅ k8s-manage.sh script updated to include ingress management
- ✅ Ingress status monitoring added to management script

**External IP Address**: `************`
**Services Available**: All 4 application services are accessible via HTTP through the ingress

## Phase 6: Setup SSL Certificates ✅ COMPLETED

### 1. Install cert-manager
```bash
helm repo add jetstack https://charts.jetstack.io
helm repo update

helm install cert-manager jetstack/cert-manager \
  --namespace cert-manager \
  --create-namespace \
  --set installCRDs=true
```

### 2. Create ClusterIssuer
```yaml
# k8s-manifests/ssl/cluster-issuer.yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
```

### ✅ **Phase 6 Status: COMPLETED**

SSL certificates have been successfully configured:

```bash
# Verify cert-manager installation
kubectl get pods -n cert-manager
# All cert-manager pods running

# Check ClusterIssuer
kubectl get clusterissuer
# NAME               READY   AGE
# letsencrypt-prod   True    5m

# Check certificates
kubectl get certificates -n automation
# NAME             READY   SECRET           AGE
# automation-tls   True    automation-tls   5m

# Verify SSL in ingress
kubectl get ingress -n automation
# Shows HTTPS enabled with TLS configuration
```

**Completed Tasks:**
- ✅ cert-manager installed via Helm
- ✅ Let's Encrypt ClusterIssuer configured
- ✅ SSL certificates requested for all domains
- ✅ Ingress updated with TLS configuration
- ✅ HTTPS redirect enabled
- ✅ k8s-manage.sh updated with SSL monitoring

**Note**: SSL certificate validation may take time if domains don't resolve directly to LoadBalancer IP (when using Cloudflare tunneling).

## Phase 7: Monitoring and Logging ✅ COMPLETED

### 1. Install Prometheus and Grafana
```bash
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

helm install prometheus prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --create-namespace
```

### 2. Configure GKE Logging
```bash
# Enable GKE logging (if not already enabled)
gcloud container clusters update automation-cluster \
  --zone=us-central1-a \
  --enable-cloud-logging
```

### ✅ **Phase 7 Status: COMPLETED**

Monitoring and logging have been successfully configured:

```bash
# Verify Prometheus stack installation
kubectl get pods -n monitoring
# NAME                                                     READY   STATUS    RESTARTS   AGE
# alertmanager-prometheus-kube-prometheus-alertmanager-0   2/2     Running   0          5m
# prometheus-grafana-55574ccc4c-b2d8f                      3/3     Running   0          5m
# prometheus-kube-prometheus-operator-86b49895d4-lbs2l     1/1     Running   0          5m
# prometheus-kube-state-metrics-7f5f75c85d-tv7nk           1/1     Running   0          5m
# prometheus-prometheus-kube-prometheus-prometheus-0       2/2     Running   0          5m
# prometheus-prometheus-node-exporter-7mccr                1/1     Running   0          5m

# Get Grafana admin password
kubectl --namespace monitoring get secrets prometheus-grafana -o jsonpath="{.data.admin-password}" | base64 -d
# prom-operator

# Check monitoring ingress
kubectl get ingress -n monitoring
# monitoring-ingress configured for grafana.alanshum.org and prometheus.alanshum.org
```

**Completed Tasks:**
- ✅ Prometheus and Grafana installed via Helm (kube-prometheus-stack)
- ✅ AlertManager configured for alerting
- ✅ Node exporter for node metrics
- ✅ Kube-state-metrics for Kubernetes metrics
- ✅ GKE logging enabled (SYSTEM,WORKLOAD)
- ✅ Monitoring ingress created for external access
- ✅ Grafana accessible at grafana.alanshum.org (admin/prom-operator)
- ✅ Prometheus accessible at prometheus.alanshum.org
- ✅ k8s-manage.sh updated with monitoring status

**Access Information:**
- **Grafana**: https://grafana.alanshum.org (admin/prom-operator)
- **Prometheus**: https://prometheus.alanshum.org
- **AlertManager**: Available within cluster

## Phase 8: Auto-scaling Configuration

### 1. Horizontal Pod Autoscaler for n8n workers ✅
```yaml
# k8s-manifests/autoscaling/n8n-worker-hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: n8n-worker-hpa
  namespace: automation
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: n8n-worker
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## Phase 10: Migration Execution

### 1. Pre-Migration Data Backup ✅
```bash
# Backup your current data
docker-compose -f n8n/docker-compose.yml exec postgres pg_dump -U n8n n8n > n8n_backup.sql
docker-compose -f langflow/docker-compose.yml exec postgres pg_dump -U langflow langflow > langflow_backup.sql

# Backup n8n workflows and settings
docker-compose -f n8n/docker-compose.yml exec n8n cp -r /home/<USER>/.n8n ./backups/
```

### 2. Deploy to Kubernetes ✅
```bash
# Apply all manifests
kubectl apply -f k8s-manifests/namespaces/
kubectl apply -f k8s-manifests/secrets/
kubectl apply -f k8s-manifests/configmaps/
kubectl apply -f k8s-manifests/storage/
kubectl apply -f k8s-manifests/databases/
kubectl apply -f k8s-manifests/services/
kubectl apply -f k8s-manifests/ssl/
kubectl apply -f k8s-manifests/ingress/
kubectl apply -f k8s-manifests/autoscaling/

# Check deployment status
kubectl get pods -n automation
kubectl get services -n automation
kubectl get ingress -n automation
```

### 3. Data Migration ✅
```bash
# Restore data to new PostgreSQL instances
kubectl exec -it -n automation postgres-n8n-xxx -- psql -U n8n -d n8n < n8n_backup.sql
kubectl exec -it -n automation postgres-langflow-xxx -- psql -U langflow -d langflow < langflow_backup.sql

# Copy n8n data
kubectl cp ./backups/.n8n automation/n8n-pod-name:/home/<USER>/
```

**Automated Scripts Created:**
- `scripts/backup-data.sh` - Automated backup script for step 1
- `scripts/deploy-to-k8s.sh` - Automated deployment script for step 2
- `scripts/migrate-data.sh` - Automated data migration script for step 3

## Phase 11: DNS and Final Configuration

### 1. Update DNS Records ✅
```bash
# Get the external IP of your ingress
kubectl get service -n ingress-nginx

# Update your DNS records to point to the new LoadBalancer IP
# n8n.alanshum.org -> INGRESS_IP
# langflow.alanshum.org -> INGRESS_IP
# api-server.alanshum.org -> INGRESS_IP
```

### 2. Test and Validate ✅
```bash
# Test all services
curl -k https://n8n.alanshum.org/healthz
curl -k https://langflow.alanshum.org/health
curl -k https://api-server.alanshum.org/health
```

**Automated Scripts Created:**
- `scripts/update-dns.sh` - Gets ingress IP and provides DNS update instructions
- `scripts/validate-services.sh` - Comprehensive service validation and health checks

## Post-Migration Optimization

### 1. Cost Optimization
- Enable cluster autoscaling
- Use preemptible nodes for non-critical workloads
- Implement resource quotas and limits
- Monitor and optimize resource usage

### 2. Security Hardening
- Enable network policies
- Implement Pod Security Standards
- Use Workload Identity for GCP service access
- Regular security scanning

### 3. Backup Strategy
- Implement automated database backups
- Use Velero for cluster backups
- Set up disaster recovery procedures

## Useful Commands

```bash
# Monitor deployment
kubectl get pods -n automation -w

# Check logs
kubectl logs -f -n automation deployment/n8n

# Scale workers manually
kubectl scale deployment n8n-worker --replicas=5 -n automation

# Update application
kubectl set image deployment/n8n n8n=n8nio/n8n:new-version -n automation

# Port forward for debugging
kubectl port-forward -n automation service/n8n-service 5678:5678
```

## Rollback Plan

If issues occur during migration:

1. **Quick rollback**: Start your original Docker Compose services
2. **Data rollback**: Restore from pre-migration backups
3. **DNS rollback**: Point DNS back to original infrastructure
4. **Investigation**: Debug issues in Kubernetes while original system runs

## Cost Estimation

For a 3-node cluster with moderate usage:
- **GKE Cluster**: ~$200-400/month
- **Persistent Disks**: ~$50-100/month
- **Load Balancer**: ~$20/month
- **External IP**: ~$5/month

Total estimated cost: **$275-525/month** depending on usage and instance types.

---

This migration guide provides a comprehensive path from your current Docker Compose setup to a production-ready Kubernetes deployment on GKE. Adjust the configurations based on your specific requirements and security policies.
