#!/bin/bash

# n8n Backup Setup Script
# This script helps you set up the n8n workflow backup system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local missing_deps=()
    
    if ! command_exists curl; then
        missing_deps+=("curl")
    fi
    
    if ! command_exists jq; then
        missing_deps+=("jq")
    fi
    
    if ! command_exists git; then
        missing_deps+=("git")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        echo ""
        echo "Please install the missing dependencies:"
        
        if [[ "$OSTYPE" == "darwin"* ]]; then
            echo "  brew install ${missing_deps[*]}"
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            echo "  sudo apt-get install ${missing_deps[*]}"
        fi
        
        exit 1
    fi
    
    log_success "All prerequisites are installed"
}

# Function to test n8n connectivity
test_n8n_connectivity() {
    local n8n_host="$1"
    local api_key="$2"
    
    log_info "Testing n8n connectivity..."
    
    local response
    response=$(curl -s -w "%{http_code}" -H "X-N8N-API-KEY: $api_key" "$n8n_host/api/v1/workflows" -o /dev/null)
    
    if [ "$response" = "200" ]; then
        log_success "n8n API connectivity verified"
        return 0
    else
        log_error "Failed to connect to n8n API (HTTP $response)"
        return 1
    fi
}

# Function to get workflow count
get_workflow_count() {
    local n8n_host="$1"
    local api_key="$2"
    
    local workflows_response
    workflows_response=$(curl -s -H "X-N8N-API-KEY: $api_key" "$n8n_host/api/v1/workflows")
    
    if [ $? -eq 0 ]; then
        echo "$workflows_response" | jq '.data | length'
    else
        echo "0"
    fi
}

# Function to setup environment variables
setup_environment() {
    log_info "Setting up environment variables..."
    
    # Get n8n host
    local default_host="https://n8n.alanshum.org"
    echo ""
    read -p "Enter your n8n host URL [$default_host]: " n8n_host
    n8n_host=${n8n_host:-$default_host}
    
    # Get API key
    echo ""
    log_info "You need to generate an API key in your n8n instance:"
    log_info "1. Go to $n8n_host"
    log_info "2. Navigate to Settings > API Keys"
    log_info "3. Click 'Create API Key'"
    log_info "4. Copy the generated key"
    echo ""
    
    read -s -p "Enter your n8n API key: " api_key
    echo ""
    
    if [ -z "$api_key" ]; then
        log_error "API key is required"
        exit 1
    fi
    
    # Test connectivity
    if ! test_n8n_connectivity "$n8n_host" "$api_key"; then
        log_error "Failed to connect to n8n. Please check your host URL and API key."
        exit 1
    fi
    
    # Get workflow count
    local workflow_count
    workflow_count=$(get_workflow_count "$n8n_host" "$api_key")
    log_info "Found $workflow_count workflows in your n8n instance"
    
    # Save to shell profile
    local shell_profile=""
    if [ -n "$ZSH_VERSION" ]; then
        shell_profile="$HOME/.zshrc"
    elif [ -n "$BASH_VERSION" ]; then
        shell_profile="$HOME/.bashrc"
    fi
    
    if [ -n "$shell_profile" ] && [ -f "$shell_profile" ]; then
        echo ""
        read -p "Save environment variables to $shell_profile? (y/N): " save_to_profile
        
        if [[ "$save_to_profile" =~ ^[Yy]$ ]]; then
            echo "" >> "$shell_profile"
            echo "# n8n Backup Configuration" >> "$shell_profile"
            echo "export N8N_HOST=\"$n8n_host\"" >> "$shell_profile"
            echo "export N8N_API_KEY=\"$api_key\"" >> "$shell_profile"
            
            log_success "Environment variables saved to $shell_profile"
            log_info "Run 'source $shell_profile' or restart your terminal to apply changes"
        fi
    fi
    
    # Export for current session
    export N8N_HOST="$n8n_host"
    export N8N_API_KEY="$api_key"
    
    log_success "Environment setup completed"
}

# Function to run test backup
run_test_backup() {
    log_info "Running test backup..."
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local backup_script="$script_dir/backup-n8n-workflows.sh"
    
    if [ ! -f "$backup_script" ]; then
        log_error "Backup script not found: $backup_script"
        exit 1
    fi
    
    # Run backup with no-archive and no-git flags for testing
    if "$backup_script" --no-archive --no-git; then
        log_success "Test backup completed successfully"
        
        # Show what was exported
        local repo_root="$(dirname "$script_dir")"
        local latest_export=$(find "$repo_root/n8n-workflows/exports" -name "backup_info.json" -type f | sort | tail -1)
        
        if [ -f "$latest_export" ]; then
            local export_dir=$(dirname "$latest_export")
            local workflow_count=$(jq -r '.exported_workflows' "$latest_export")
            
            log_info "Exported $workflow_count workflows to: $export_dir"
            log_info "You can find the exported workflow files in that directory"
        fi
    else
        log_error "Test backup failed"
        exit 1
    fi
}

# Function to setup GitHub secrets
setup_github_secrets() {
    log_info "Setting up GitHub repository secrets..."
    echo ""
    log_info "To enable automated backups, you need to add secrets to your GitHub repository:"
    echo ""
    echo "1. Go to your repository on GitHub"
    echo "2. Navigate to Settings > Secrets and variables > Actions"
    echo "3. Click 'New repository secret'"
    echo "4. Add the following secrets:"
    echo ""
    echo "   Name: N8N_API_KEY"
    echo "   Value: $N8N_API_KEY"
    echo ""
    echo "   Name: N8N_HOST (optional, defaults to https://n8n.alanshum.org)"
    echo "   Value: $N8N_HOST"
    echo ""
    log_info "Once added, the GitHub Actions workflow will run daily backups automatically"
}

# Function to show next steps
show_next_steps() {
    echo ""
    log_success "Setup completed! Here's what you can do next:"
    echo ""
    echo "1. Run a manual backup:"
    echo "   ./scripts/backup-n8n-workflows.sh"
    echo ""
    echo "2. List available backups:"
    echo "   ./scripts/restore-n8n-workflows.sh --list"
    echo ""
    echo "3. Set up automated backups by adding GitHub secrets (see above)"
    echo ""
    echo "4. Read the full documentation:"
    echo "   cat n8n-workflows/README.md"
    echo ""
}

# Main function
main() {
    echo "n8n Workflow Backup Setup"
    echo "========================="
    echo ""
    
    check_prerequisites
    setup_environment
    
    echo ""
    read -p "Run a test backup now? (Y/n): " run_test
    
    if [[ ! "$run_test" =~ ^[Nn]$ ]]; then
        run_test_backup
    fi
    
    setup_github_secrets
    show_next_steps
}

# Run main function
main "$@"
