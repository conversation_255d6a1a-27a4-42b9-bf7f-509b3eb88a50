#!/bin/bash

# Service Validation Script
# This script tests and validates all deployed services

set -e

echo "Starting service validation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test a service
test_service() {
    local name=$1
    local url=$2
    local expected_status=${3:-200}
    
    echo -n "Testing $name... "
    
    # Test with curl
    if response=$(curl -s -k -w "%{http_code}" -o /dev/null --connect-timeout 10 --max-time 30 "$url" 2>/dev/null); then
        if [ "$response" = "$expected_status" ]; then
            echo -e "${GREEN}✅ PASS${NC} (HTTP $response)"
            return 0
        else
            echo -e "${YELLOW}⚠️  WARN${NC} (HTTP $response, expected $expected_status)"
            return 1
        fi
    else
        echo -e "${RED}❌ FAIL${NC} (Connection failed)"
        return 1
    fi
}

# Function to test DNS resolution
test_dns() {
    local domain=$1
    echo -n "Testing DNS for $domain... "
    
    if nslookup "$domain" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ RESOLVED${NC}"
        return 0
    else
        echo -e "${RED}❌ NOT RESOLVED${NC}"
        return 1
    fi
}

echo ""
echo "🔍 DNS Resolution Tests"
echo "========================"

dns_tests=0
dns_passed=0

for domain in "n8n.alanshum.org" "langflow.alanshum.org" "api-server.alanshum.org" "grafana.alanshum.org" "prometheus.alanshum.org"; do
    dns_tests=$((dns_tests + 1))
    if test_dns "$domain"; then
        dns_passed=$((dns_passed + 1))
    fi
done

echo ""
echo "🌐 Service Health Tests"
echo "========================"

service_tests=0
service_passed=0

# Test n8n
service_tests=$((service_tests + 1))
if test_service "n8n" "https://n8n.alanshum.org/healthz"; then
    service_passed=$((service_passed + 1))
fi

# Test langflow
service_tests=$((service_tests + 1))
if test_service "langflow" "https://langflow.alanshum.org/health"; then
    service_passed=$((service_passed + 1))
fi

# Test API server
service_tests=$((service_tests + 1))
if test_service "api-server" "https://api-server.alanshum.org/health"; then
    service_passed=$((service_passed + 1))
fi

# Test Grafana
service_tests=$((service_tests + 1))
if test_service "grafana" "https://grafana.alanshum.org/api/health" "200"; then
    service_passed=$((service_passed + 1))
fi

# Test Prometheus
service_tests=$((service_tests + 1))
if test_service "prometheus" "https://prometheus.alanshum.org/-/healthy" "200"; then
    service_passed=$((service_passed + 1))
fi

echo ""
echo "📊 Kubernetes Cluster Status"
echo "============================="

echo "Pods in automation namespace:"
kubectl get pods -n automation

echo ""
echo "Services in automation namespace:"
kubectl get services -n automation

echo ""
echo "Ingress status:"
kubectl get ingress -n automation

echo ""
echo "HPA status:"
kubectl get hpa -n automation

echo ""
echo "📋 Test Summary"
echo "==============="
echo -e "DNS Tests:     ${dns_passed}/${dns_tests} passed"
echo -e "Service Tests: ${service_passed}/${service_tests} passed"

if [ $dns_passed -eq $dns_tests ] && [ $service_passed -eq $service_tests ]; then
    echo -e "${GREEN}🎉 All tests passed! Your migration is successful!${NC}"
    echo ""
    echo "🔗 Access your services:"
    echo "  • n8n:        https://n8n.alanshum.org"
    echo "  • Langflow:   https://langflow.alanshum.org"
    echo "  • API Server: https://api-server.alanshum.org"
    echo "  • Grafana:    https://grafana.alanshum.org"
    echo "  • Prometheus: https://prometheus.alanshum.org"
    exit 0
elif [ $dns_passed -lt $dns_tests ]; then
    echo -e "${YELLOW}⚠️  Some DNS tests failed. Please check your DNS configuration.${NC}"
    exit 1
else
    echo -e "${YELLOW}⚠️  Some services are not responding. Please check the deployment.${NC}"
    exit 1
fi
