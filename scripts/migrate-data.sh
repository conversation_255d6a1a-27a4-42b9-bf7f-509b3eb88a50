#!/bin/bash

# Data Migration Script
# This script migrates data from backups to the new Kubernetes deployment

set -e

echo "Starting data migration..."

# Check if backup files exist
if [ ! -f "backups/n8n_backup.sql" ]; then
    echo "❌ n8n backup file not found. Please run backup-data.sh first."
    exit 1
fi

if [ ! -f "backups/langflow_backup.sql" ]; then
    echo "❌ langflow backup file not found. Please run backup-data.sh first."
    exit 1
fi

if [ ! -d "backups/.n8n" ]; then
    echo "❌ n8n workflows backup not found. Please run backup-data.sh first."
    exit 1
fi

echo "✅ Backup files found"

# Get pod names
N8N_POSTGRES_POD=$(kubectl get pods -n automation -l app=postgres-n8n -o jsonpath='{.items[0].metadata.name}')
LANGFLOW_POSTGRES_POD=$(kubectl get pods -n automation -l app=postgres-langflow -o jsonpath='{.items[0].metadata.name}')
N8N_POD=$(kubectl get pods -n automation -l app=n8n -o jsonpath='{.items[0].metadata.name}')

echo "Found pods:"
echo "  N8N Postgres: $N8N_POSTGRES_POD"
echo "  Langflow Postgres: $LANGFLOW_POSTGRES_POD"
echo "  N8N: $N8N_POD"

# Restore n8n database
echo "Restoring n8n database..."
kubectl exec -i -n automation $N8N_POSTGRES_POD -- psql -U n8n -d n8n < backups/n8n_backup.sql
echo "✅ n8n database restored"

# Restore langflow database
echo "Restoring langflow database..."
kubectl exec -i -n automation $LANGFLOW_POSTGRES_POD -- psql -U langflow -d langflow < backups/langflow_backup.sql
echo "✅ langflow database restored"

# Copy n8n workflows and settings
echo "Copying n8n workflows and settings..."
kubectl cp backups/.n8n automation/$N8N_POD:/home/<USER>/
echo "✅ n8n workflows and settings copied"

# Restart n8n to pick up the new data
echo "Restarting n8n to apply changes..."
kubectl rollout restart deployment/n8n -n automation
kubectl rollout status deployment/n8n -n automation

echo ""
echo "Data migration completed successfully! 🎉"
echo ""
echo "Your applications should now have all the data from your previous deployment."
