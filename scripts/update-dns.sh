#!/bin/bash

# DNS Update Script
# This script helps you get the ingress IP and provides DNS update instructions

set -e

echo "Getting ingress IP for DNS configuration..."

# Check if kubectl is available and cluster is accessible
if ! kubectl cluster-info &> /dev/null; then
    echo "❌ kubectl not available or cluster not accessible"
    echo "Please ensure you're connected to your Kubernetes cluster"
    exit 1
fi

echo "✅ Kubernetes cluster accessible"

# Get the external IP of the ingress controller
echo "Checking ingress controller service..."

# Try different common ingress controller namespaces and names
INGRESS_IP=""
INGRESS_SERVICE=""

# Check for nginx-ingress
if kubectl get service -n ingress-nginx ingress-nginx-controller &> /dev/null; then
    INGRESS_SERVICE="ingress-nginx-controller"
    NAMESPACE="ingress-nginx"
elif kubectl get service -n nginx-ingress nginx-ingress-controller &> /dev/null; then
    INGRESS_SERVICE="nginx-ingress-controller"
    NAMESPACE="nginx-ingress"
elif kubectl get service -n kube-system nginx-ingress-controller &> /dev/null; then
    INGRESS_SERVICE="nginx-ingress-controller"
    NAMESPACE="kube-system"
else
    echo "❌ Could not find ingress controller service"
    echo "Please check your ingress controller installation"
    echo ""
    echo "Available services:"
    kubectl get services --all-namespaces | grep -i ingress || echo "No ingress services found"
    exit 1
fi

echo "✅ Found ingress service: $INGRESS_SERVICE in namespace $NAMESPACE"

# Get the external IP
INGRESS_IP=$(kubectl get service -n $NAMESPACE $INGRESS_SERVICE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')

if [ -z "$INGRESS_IP" ]; then
    # Try to get hostname instead of IP (for some cloud providers)
    INGRESS_HOSTNAME=$(kubectl get service -n $NAMESPACE $INGRESS_SERVICE -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
    if [ -n "$INGRESS_HOSTNAME" ]; then
        echo "✅ Ingress hostname: $INGRESS_HOSTNAME"
        echo ""
        echo "🔧 DNS Configuration Required:"
        echo "================================================"
        echo "Update your DNS records to point to: $INGRESS_HOSTNAME"
        echo ""
        echo "Required DNS Records (CNAME):"
        echo "  n8n.alanshum.org          -> $INGRESS_HOSTNAME"
        echo "  langflow.alanshum.org     -> $INGRESS_HOSTNAME"
        echo "  api-server.alanshum.org   -> $INGRESS_HOSTNAME"
        echo "  grafana.alanshum.org      -> $INGRESS_HOSTNAME"
        echo "  prometheus.alanshum.org   -> $INGRESS_HOSTNAME"
        echo "================================================"
    else
        echo "❌ No external IP or hostname found for ingress"
        echo "The LoadBalancer might still be provisioning..."
        echo ""
        echo "Current service status:"
        kubectl get service -n $NAMESPACE $INGRESS_SERVICE
        echo ""
        echo "Please wait a few minutes and run this script again."
        exit 1
    fi
else
    echo "✅ Ingress IP: $INGRESS_IP"
    echo ""
    echo "🔧 DNS Configuration Required:"
    echo "================================================"
    echo "Update your DNS records to point to: $INGRESS_IP"
    echo ""
    echo "Required DNS Records (A Records):"
    echo "  n8n.alanshum.org          -> $INGRESS_IP"
    echo "  langflow.alanshum.org     -> $INGRESS_IP"
    echo "  api-server.alanshum.org   -> $INGRESS_IP"
    echo "  grafana.alanshum.org      -> $INGRESS_IP"
    echo "  prometheus.alanshum.org   -> $INGRESS_IP"
    echo "================================================"
fi

echo ""
echo "📝 Instructions:"
echo "1. Log into your DNS provider (e.g., Cloudflare, Route53, etc.)"
echo "2. Update the DNS records as shown above"
echo "3. Wait for DNS propagation (usually 5-15 minutes)"
echo "4. Run the validation script: ./scripts/validate-services.sh"
echo ""
echo "✅ DNS update information generated successfully!"
