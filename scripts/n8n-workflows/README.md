# n8n Workflow Management Scripts

This directory contains scripts for managing n8n workflows, including backup, restoration, and setup functionality.

## Scripts Overview

### `backup-n8n-workflows.sh`
Main backup script that exports workflows from your n8n instance and creates versioned backups.

**Features:**
- Exports all workflows via n8n API
- Creates timestamped exports and compressed archives
- Supports both manual and automated backups
- Automatic Git commits for version control
- Configurable retention policies

**Usage:**
```bash
# Basic manual backup
./backup-n8n-workflows.sh

# Daily automated backup
./backup-n8n-workflows.sh --type daily

# Export only (no archive/git)
./backup-n8n-workflows.sh --no-git --no-archive
```

### `restore-n8n-workflows.sh`
Restoration script for recovering workflows from backups.

**Features:**
- Restore individual workflows or entire backup sets
- List available backups and workflows
- Handle workflow conflicts with force update options
- Validate workflow data before restoration

**Usage:**
```bash
# List available backups
./restore-n8n-workflows.sh --list

# Restore all workflows from a backup
./restore-n8n-workflows.sh --all 20240101_120000

# Restore single workflow
./restore-n8n-workflows.sh --single workflow.json
```

### `setup-n8n-backup.sh`
Interactive setup script for configuring the backup system.

**Features:**
- Guided setup process
- API connectivity testing
- Environment validation
- Test backup execution
- GitHub Actions setup instructions

**Usage:**
```bash
./setup-n8n-backup.sh
```

## Configuration

All scripts require the following environment variables:

- `N8N_API_KEY`: Your n8n API key (required)
- `N8N_HOST`: Your n8n instance URL (defaults to https://n8n.alanshum.org)

## Directory Structure

The scripts work with the following directory structure:
```
automation-agent/
├── n8n-workflows/           # Main backup directory
│   ├── exports/             # Raw exported workflow JSON files
│   └── backups/             # Compressed backup archives
│       ├── daily/           # Daily automated backups
│       └── manual/          # Manual backup archives
└── scripts/
    └── n8n-workflows/       # This directory
        ├── backup-n8n-workflows.sh
        ├── restore-n8n-workflows.sh
        └── setup-n8n-backup.sh
```

## Getting Started

1. **First-time setup:**
   ```bash
   ./setup-n8n-backup.sh
   ```

2. **Manual backup:**
   ```bash
   export N8N_API_KEY="your-api-key"
   ./backup-n8n-workflows.sh
   ```

3. **Automated backups:**
   - Add `N8N_API_KEY` to GitHub repository secrets
   - GitHub Actions will run daily backups automatically

For detailed documentation, see [../../n8n-workflows/README.md](../../n8n-workflows/README.md).
