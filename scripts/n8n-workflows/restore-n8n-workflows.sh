#!/bin/bash

# n8n Workflow Restoration Script
# This script helps restore workflows from backups to your n8n instance

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
BACKUP_DIR="$REPO_ROOT/n8n-workflows"
EXPORTS_DIR="$BACKUP_DIR/exports"

# n8n Configuration
N8N_HOST="${N8N_HOST:-https://n8n.alanshum.org}"
N8N_API_KEY="${N8N_API_KEY:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed"
        exit 1
    fi
    
    # Check if jq is available
    if ! command -v jq &> /dev/null; then
        log_error "jq is required but not installed. Please install it with: brew install jq"
        exit 1
    fi
    
    log_success "All prerequisites met"
}

# Function to check n8n API connectivity
check_n8n_api() {
    log_info "Checking n8n API connectivity..."
    
    if [ -z "$N8N_API_KEY" ]; then
        log_error "N8N_API_KEY environment variable is not set"
        log_info "Please set your n8n API key: export N8N_API_KEY='your-api-key'"
        exit 1
    fi
    
    # Test API connectivity
    local response
    response=$(curl -k -s -w "%{http_code}" -H "X-N8N-API-KEY: $N8N_API_KEY" -H "accept: application/json" "$N8N_HOST/api/v1/workflows" -o /dev/null)
    
    if [ "$response" != "200" ]; then
        log_error "Failed to connect to n8n API. HTTP status: $response"
        exit 1
    fi
    
    log_success "n8n API connectivity verified"
}

# Function to list available backups
list_backups() {
    log_info "Available backup exports:"
    echo ""
    
    if [ ! -d "$EXPORTS_DIR" ]; then
        log_warning "No exports directory found"
        return 1
    fi
    
    local count=0
    for backup_dir in "$EXPORTS_DIR"/*; do
        if [ -d "$backup_dir" ]; then
            local backup_name=$(basename "$backup_dir")
            local info_file="$backup_dir/backup_info.json"
            
            if [ -f "$info_file" ]; then
                local backup_date=$(jq -r '.export_date' "$info_file" 2>/dev/null || echo "Unknown")
                local workflow_count=$(jq -r '.total_workflows' "$info_file" 2>/dev/null || echo "Unknown")
                local backup_type=$(jq -r '.backup_type' "$info_file" 2>/dev/null || echo "Unknown")
                
                printf "  %2d. %s (%s, %s workflows, %s)\n" $((++count)) "$backup_name" "$backup_type" "$workflow_count" "$backup_date"
            else
                printf "  %2d. %s (No metadata available)\n" $((++count)) "$backup_name"
            fi
        fi
    done
    
    if [ $count -eq 0 ]; then
        log_warning "No backup exports found"
        return 1
    fi
    
    echo ""
    return 0
}

# Function to list workflows in a backup
list_workflows_in_backup() {
    local backup_dir="$1"
    
    log_info "Workflows in backup $(basename "$backup_dir"):"
    echo ""
    
    local count=0
    for workflow_file in "$backup_dir"/*.json; do
        if [ -f "$workflow_file" ] && [ "$(basename "$workflow_file")" != "backup_info.json" ]; then
            local workflow_name=$(jq -r '.name' "$workflow_file" 2>/dev/null || echo "Unknown")
            local workflow_id=$(jq -r '.id' "$workflow_file" 2>/dev/null || echo "Unknown")
            local filename=$(basename "$workflow_file")
            
            printf "  %2d. %s (ID: %s) [%s]\n" $((++count)) "$workflow_name" "$workflow_id" "$filename"
        fi
    done
    
    if [ $count -eq 0 ]; then
        log_warning "No workflow files found in backup"
        return 1
    fi
    
    echo ""
    return 0
}

# Function to restore a single workflow
restore_workflow() {
    local workflow_file="$1"
    local force_update="$2"
    
    if [ ! -f "$workflow_file" ]; then
        log_error "Workflow file not found: $workflow_file"
        return 1
    fi
    
    local workflow_name=$(jq -r '.name' "$workflow_file")
    local workflow_id=$(jq -r '.id' "$workflow_file")
    
    log_info "Restoring workflow: $workflow_name (ID: $workflow_id)"
    
    # Check if workflow already exists
    local existing_workflow
    existing_workflow=$(curl -k -s -H "X-N8N-API-KEY: $N8N_API_KEY" -H "accept: application/json" "$N8N_HOST/api/v1/workflows/$workflow_id" 2>/dev/null)
    
    if [ $? -eq 0 ] && echo "$existing_workflow" | jq -e '.id' >/dev/null 2>&1; then
        if [ "$force_update" != "true" ]; then
            log_warning "Workflow already exists. Use --force to overwrite."
            return 1
        else
            log_info "Updating existing workflow..."
            # Update existing workflow
            local response
            response=$(curl -k -s -X PUT \
                -H "X-N8N-API-KEY: $N8N_API_KEY" \
                -H "Content-Type: application/json" \
                -H "accept: application/json" \
                -d @"$workflow_file" \
                "$N8N_HOST/api/v1/workflows/$workflow_id")
            
            if echo "$response" | jq -e '.id' >/dev/null 2>&1; then
                log_success "Workflow updated successfully"
                return 0
            else
                log_error "Failed to update workflow: $response"
                return 1
            fi
        fi
    else
        log_info "Creating new workflow..."
        # Create new workflow (remove ID to let n8n assign a new one)
        local workflow_data
        workflow_data=$(jq 'del(.id)' "$workflow_file")
        
        local response
        response=$(curl -k -s -X POST \
            -H "X-N8N-API-KEY: $N8N_API_KEY" \
            -H "Content-Type: application/json" \
            -H "accept: application/json" \
            -d "$workflow_data" \
            "$N8N_HOST/api/v1/workflows")
        
        if echo "$response" | jq -e '.id' >/dev/null 2>&1; then
            local new_id=$(echo "$response" | jq -r '.id')
            log_success "Workflow created successfully with new ID: $new_id"
            return 0
        else
            log_error "Failed to create workflow: $response"
            return 1
        fi
    fi
}

# Function to restore all workflows from a backup
restore_all_workflows() {
    local backup_dir="$1"
    local force_update="$2"
    
    log_info "Restoring all workflows from backup $(basename "$backup_dir")..."
    
    local success_count=0
    local error_count=0
    
    for workflow_file in "$backup_dir"/*.json; do
        if [ -f "$workflow_file" ] && [ "$(basename "$workflow_file")" != "backup_info.json" ]; then
            if restore_workflow "$workflow_file" "$force_update"; then
                ((success_count++))
            else
                ((error_count++))
            fi
        fi
    done
    
    log_info "Restoration completed: $success_count successful, $error_count failed"
    
    if [ $error_count -gt 0 ]; then
        return 1
    fi
    
    return 0
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [BACKUP_DIR|WORKFLOW_FILE]"
    echo ""
    echo "Options:"
    echo "  -l, --list              List available backups"
    echo "  -w, --workflows BACKUP  List workflows in a specific backup"
    echo "  -f, --force             Force update existing workflows"
    echo "  -a, --all BACKUP        Restore all workflows from backup"
    echo "  -s, --single FILE       Restore single workflow file"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  N8N_HOST               n8n instance URL (default: https://n8n.alanshum.org)"
    echo "  N8N_API_KEY            n8n API key (required)"
    echo ""
    echo "Examples:"
    echo "  $0 --list                                    # List available backups"
    echo "  $0 --workflows 20240101_120000               # List workflows in backup"
    echo "  $0 --all 20240101_120000                     # Restore all workflows"
    echo "  $0 --single backup.json                      # Restore single workflow"
    echo "  $0 --all 20240101_120000 --force             # Force restore all workflows"
}

# Main function
main() {
    local action=""
    local backup_dir=""
    local workflow_file=""
    local force_update="false"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -l|--list)
                action="list"
                shift
                ;;
            -w|--workflows)
                action="list_workflows"
                backup_dir="$2"
                shift 2
                ;;
            -a|--all)
                action="restore_all"
                backup_dir="$2"
                shift 2
                ;;
            -s|--single)
                action="restore_single"
                workflow_file="$2"
                shift 2
                ;;
            -f|--force)
                force_update="true"
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                if [ -z "$action" ]; then
                    log_error "Unknown option: $1"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # Default action if none specified
    if [ -z "$action" ]; then
        action="list"
    fi
    
    # Execute action
    case $action in
        list)
            list_backups
            ;;
        list_workflows)
            if [ -z "$backup_dir" ]; then
                log_error "Backup directory required for --workflows option"
                exit 1
            fi
            
            local full_backup_dir="$EXPORTS_DIR/$backup_dir"
            if [ ! -d "$full_backup_dir" ]; then
                log_error "Backup directory not found: $full_backup_dir"
                exit 1
            fi
            
            list_workflows_in_backup "$full_backup_dir"
            ;;
        restore_all)
            if [ -z "$backup_dir" ]; then
                log_error "Backup directory required for --all option"
                exit 1
            fi
            
            local full_backup_dir="$EXPORTS_DIR/$backup_dir"
            if [ ! -d "$full_backup_dir" ]; then
                log_error "Backup directory not found: $full_backup_dir"
                exit 1
            fi
            
            check_prerequisites
            check_n8n_api
            restore_all_workflows "$full_backup_dir" "$force_update"
            ;;
        restore_single)
            if [ -z "$workflow_file" ]; then
                log_error "Workflow file required for --single option"
                exit 1
            fi
            
            check_prerequisites
            check_n8n_api
            restore_workflow "$workflow_file" "$force_update"
            ;;
        *)
            log_error "Unknown action: $action"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
