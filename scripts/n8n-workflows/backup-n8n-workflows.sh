#!/bin/bash

# n8n Workflow Backup Script
# This script exports all workflows from your n8n instance and saves them to the repository
# It supports both manual and automated backups with proper versioning

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
BACKUP_DIR="$REPO_ROOT/n8n-workflows"
EXPORTS_DIR="$BACKUP_DIR/exports"
DAILY_BACKUP_DIR="$BACKUP_DIR/backups/daily"
MANUAL_BACKUP_DIR="$BACKUP_DIR/backups/manual"

# n8n Configuration
N8N_HOST="${N8N_HOST:-https://n8n.alanshum.org}"
N8N_API_KEY="${N8N_API_KEY:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed"
        exit 1
    fi

    # Check if jq is available
    if ! command -v jq &> /dev/null; then
        log_error "jq is required but not installed. Please install it with: brew install jq"
        exit 1
    fi

    # Check if git is available
    if ! command -v git &> /dev/null; then
        log_error "git is required but not installed"
        exit 1
    fi

    # Check if we're in a git repository
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_error "This script must be run from within a git repository"
        exit 1
    fi

    log_success "All prerequisites met"
}

# Function to check n8n API connectivity
check_n8n_api() {
    log_info "Checking n8n API connectivity..."

    if [ -z "$N8N_API_KEY" ]; then
        log_error "N8N_API_KEY environment variable is not set"
        log_info "Please set your n8n API key: export N8N_API_KEY='your-api-key'"
        log_info "You can generate an API key in n8n: Settings > API Keys"
        exit 1
    fi

    # Test API connectivity
    local response
    response=$(curl -k -s -w "%{http_code}" -H "X-N8N-API-KEY: $N8N_API_KEY" -H "accept: application/json" "$N8N_HOST/api/v1/workflows" -o /dev/null)

    if [ "$response" != "200" ]; then
        log_error "Failed to connect to n8n API. HTTP status: $response"
        log_error "Please check your N8N_HOST ($N8N_HOST) and N8N_API_KEY"
        exit 1
    fi

    log_success "n8n API connectivity verified"
}

# Function to create backup directories
create_backup_dirs() {
    log_info "Creating backup directories..."

    mkdir -p "$EXPORTS_DIR"
    mkdir -p "$DAILY_BACKUP_DIR"
    mkdir -p "$MANUAL_BACKUP_DIR"

    log_success "Backup directories created"
}

# Function to export all workflows
export_workflows() {
    local timestamp="$1"
    local backup_type="$2"

    log_info "Exporting workflows from n8n..."

    # Get list of all workflows
    local workflows_response
    workflows_response=$(curl -k -s -H "X-N8N-API-KEY: $N8N_API_KEY" -H "accept: application/json" "$N8N_HOST/api/v1/workflows")

    if [ $? -ne 0 ]; then
        log_error "Failed to fetch workflows list"
        exit 1
    fi

    # Parse workflow count
    local workflow_count
    workflow_count=$(echo "$workflows_response" | jq '.data | length')

    if [ "$workflow_count" -eq 0 ]; then
        log_warning "No workflows found in n8n instance"
        return 0
    fi

    log_info "Found $workflow_count workflows to export"

    # Create timestamped export directory
    local export_dir="$EXPORTS_DIR/$timestamp"
    mkdir -p "$export_dir"

    # Export each workflow
    local exported_count=0
    echo "$workflows_response" | jq -r '.data[] | @base64' | while IFS= read -r workflow_data; do
        local workflow_json
        workflow_json=$(echo "$workflow_data" | base64 --decode)

        local workflow_id
        workflow_id=$(echo "$workflow_json" | jq -r '.id')

        local workflow_name
        workflow_name=$(echo "$workflow_json" | jq -r '.name')

        # Sanitize filename
        local safe_name
        safe_name=$(echo "$workflow_name" | sed 's/[^a-zA-Z0-9._-]/_/g')

        # Export individual workflow
        local workflow_export
        workflow_export=$(curl -k -s -H "X-N8N-API-KEY: $N8N_API_KEY" -H "accept: application/json" "$N8N_HOST/api/v1/workflows/$workflow_id")

        if [ $? -eq 0 ]; then
            echo "$workflow_export" | jq '.' > "$export_dir/${safe_name}_${workflow_id}.json"
            log_info "Exported: $workflow_name (ID: $workflow_id)"
            ((exported_count++))
        else
            log_error "Failed to export workflow: $workflow_name (ID: $workflow_id)"
        fi
    done

    # Create a summary file
    cat > "$export_dir/backup_info.json" << EOF
{
  "timestamp": "$timestamp",
  "backup_type": "$backup_type",
  "n8n_host": "$N8N_HOST",
  "total_workflows": $workflow_count,
  "exported_workflows": $exported_count,
  "export_date": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
}
EOF

    log_success "Exported $exported_count workflows to $export_dir"
    echo "$export_dir"
}

# Function to create backup archive
create_backup_archive() {
    local export_dir="$1"
    local backup_type="$2"
    local timestamp="$3"

    log_info "Creating backup archive..."

    local backup_dir
    if [ "$backup_type" = "daily" ]; then
        backup_dir="$DAILY_BACKUP_DIR"
    else
        backup_dir="$MANUAL_BACKUP_DIR"
    fi

    local archive_name="n8n_workflows_${timestamp}.tar.gz"
    local archive_path="$backup_dir/$archive_name"

    # Create archive
    tar -czf "$archive_path" -C "$EXPORTS_DIR" "$(basename "$export_dir")"

    if [ $? -eq 0 ]; then
        log_success "Created backup archive: $archive_path"
    else
        log_error "Failed to create backup archive"
        exit 1
    fi

    echo "$archive_path"
}

# Function to commit changes to git
commit_to_git() {
    local backup_type="$1"
    local timestamp="$2"

    log_info "Committing changes to git..."

    cd "$REPO_ROOT"

    # Add all changes in the n8n-workflows directory
    git add n8n-workflows/

    # Check if there are any changes to commit
    if git diff --staged --quiet; then
        log_info "No changes to commit"
        return 0
    fi

    # Commit changes
    local commit_message="Backup n8n workflows - $backup_type backup ($timestamp)"
    git commit -m "$commit_message"

    if [ $? -eq 0 ]; then
        log_success "Changes committed to git"
    else
        log_error "Failed to commit changes to git"
        exit 1
    fi
}

# Function to cleanup old backups
cleanup_old_backups() {
    local days_to_keep="${1:-30}"

    log_info "Cleaning up backups older than $days_to_keep days..."

    # Cleanup old daily backups
    find "$DAILY_BACKUP_DIR" -name "*.tar.gz" -mtime +$days_to_keep -delete 2>/dev/null || true

    # Cleanup old export directories
    find "$EXPORTS_DIR" -type d -mtime +$days_to_keep -exec rm -rf {} + 2>/dev/null || true

    log_success "Cleanup completed"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -t, --type TYPE     Backup type: 'daily' or 'manual' (default: manual)"
    echo "  -c, --cleanup DAYS  Cleanup backups older than DAYS (default: 30)"
    echo "  --no-git           Skip git commit"
    echo "  --no-archive       Skip creating archive file"
    echo "  -h, --help         Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  N8N_HOST           n8n instance URL (default: https://n8n.alanshum.org)"
    echo "  N8N_API_KEY        n8n API key (required)"
    echo ""
    echo "Examples:"
    echo "  $0                           # Manual backup with default settings"
    echo "  $0 --type daily              # Daily automated backup"
    echo "  $0 --cleanup 7               # Cleanup backups older than 7 days"
    echo "  $0 --no-git --no-archive     # Export only, no git commit or archive"
}

# Main function
main() {
    local backup_type="manual"
    local cleanup_days="30"
    local skip_git=false
    local skip_archive=false

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                backup_type="$2"
                shift 2
                ;;
            -c|--cleanup)
                cleanup_days="$2"
                shift 2
                ;;
            --no-git)
                skip_git=true
                shift
                ;;
            --no-archive)
                skip_archive=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # Validate backup type
    if [[ "$backup_type" != "daily" && "$backup_type" != "manual" ]]; then
        log_error "Invalid backup type: $backup_type. Must be 'daily' or 'manual'"
        exit 1
    fi

    # Generate timestamp
    local timestamp
    timestamp=$(date +"%Y%m%d_%H%M%S")

    log_info "Starting n8n workflow backup..."
    log_info "Backup type: $backup_type"
    log_info "Timestamp: $timestamp"

    # Run backup process
    check_prerequisites
    check_n8n_api
    create_backup_dirs

    local export_dir
    export_dir=$(export_workflows "$timestamp" "$backup_type")

    if [ "$skip_archive" = false ]; then
        create_backup_archive "$export_dir" "$backup_type" "$timestamp"
    fi

    if [ "$skip_git" = false ]; then
        commit_to_git "$backup_type" "$timestamp"
    fi

    cleanup_old_backups "$cleanup_days"

    log_success "n8n workflow backup completed successfully!"
    log_info "Exported workflows are available in: $export_dir"
}

# Run main function with all arguments
main "$@"
