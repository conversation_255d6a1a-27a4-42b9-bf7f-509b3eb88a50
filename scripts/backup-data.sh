#!/bin/bash

# Pre-Migration Data Backup Script
# Run this script before migrating to Kubernetes

set -e

echo "Starting pre-migration backup..."

# Create backup directory
mkdir -p backups

# Backup n8n database
echo "Backing up n8n database..."
if docker-compose -f n8n/docker-compose.yml ps | grep -q postgres; then
    docker-compose -f n8n/docker-compose.yml exec -T postgres pg_dump -U n8n n8n > backups/n8n_backup.sql
    echo "✅ n8n database backup completed"
else
    echo "⚠️  n8n postgres container not running - skipping database backup"
fi

# Backup langflow database
echo "Backing up langflow database..."
if docker-compose -f langflow/docker-compose.yml ps | grep -q postgres; then
    docker-compose -f langflow/docker-compose.yml exec -T postgres pg_dump -U langflow langflow > backups/langflow_backup.sql
    echo "✅ langflow database backup completed"
else
    echo "⚠️  langflow postgres container not running - skipping database backup"
fi

# Backup n8n workflows and settings
echo "Backing up n8n workflows and settings..."
if docker-compose -f n8n/docker-compose.yml ps | grep -q n8n; then
    docker-compose -f n8n/docker-compose.yml exec -T n8n cp -r /home/<USER>/.n8n /tmp/n8n-backup
    docker cp $(docker-compose -f n8n/docker-compose.yml ps -q n8n):/tmp/n8n-backup ./backups/.n8n
    echo "✅ n8n workflows and settings backup completed"
else
    echo "⚠️  n8n container not running - skipping workflows backup"
fi

echo "Backup completed! Files saved in ./backups/"
ls -la backups/
