#!/bin/bash

# Google Cloud DNS Setup Script for alanshum.org
# This script automates the creation of DNS zone and records for GKE services

set -e

# Configuration
PROJECT_ID="macmini-home-server"
ZONE_NAME="alanshum-org-zone"
DOMAIN="alanshum.org"
LOADBALANCER_IP="************"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if gcloud is authenticated
check_auth() {
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        print_error "Not authenticated with gcloud. Please run: gcloud auth login"
        exit 1
    fi
    print_success "Authenticated with gcloud"
}

# Function to check if project exists and is accessible
check_project() {
    if ! gcloud projects describe $PROJECT_ID &>/dev/null; then
        print_error "Project $PROJECT_ID not found or not accessible"
        exit 1
    fi
    print_success "Project $PROJECT_ID is accessible"
}

# Function to enable DNS API if not already enabled
enable_dns_api() {
    print_status "Enabling Cloud DNS API..."
    gcloud services enable dns.googleapis.com --project=$PROJECT_ID
    print_success "Cloud DNS API enabled"
}

# Function to create DNS zone
create_dns_zone() {
    print_status "Creating DNS zone: $ZONE_NAME"
    
    # Check if zone already exists
    if gcloud dns managed-zones describe $ZONE_NAME --project=$PROJECT_ID &>/dev/null; then
        print_warning "DNS zone $ZONE_NAME already exists"
        return 0
    fi
    
    gcloud dns managed-zones create $ZONE_NAME \
        --description="DNS zone for $DOMAIN - GKE automation services" \
        --dns-name="$DOMAIN." \
        --project=$PROJECT_ID
    
    print_success "DNS zone $ZONE_NAME created"
}

# Function to create DNS records
create_dns_records() {
    print_status "Creating DNS records for subdomains..."
    
    # List of subdomains to create
    SUBDOMAINS=("n8n" "langflow" "api-server" "portainer" "grafana" "prometheus")
    
    # Start transaction
    gcloud dns record-sets transaction start \
        --zone=$ZONE_NAME \
        --project=$PROJECT_ID
    
    # Add A records for each subdomain
    for subdomain in "${SUBDOMAINS[@]}"; do
        print_status "Adding A record for $subdomain.$DOMAIN"
        gcloud dns record-sets transaction add $LOADBALANCER_IP \
            --name="$subdomain.$DOMAIN." \
            --ttl=300 \
            --type=A \
            --zone=$ZONE_NAME \
            --project=$PROJECT_ID
    done
    
    # Execute transaction
    gcloud dns record-sets transaction execute \
        --zone=$ZONE_NAME \
        --project=$PROJECT_ID
    
    print_success "DNS records created successfully"
}

# Function to display nameservers
show_nameservers() {
    print_status "Getting nameservers for domain registrar update..."
    echo
    echo "=========================================="
    echo "IMPORTANT: Update your domain registrar"
    echo "=========================================="
    echo
    echo "Replace your current nameservers with these Google Cloud DNS nameservers:"
    echo

    gcloud dns managed-zones describe $ZONE_NAME \
        --project=$PROJECT_ID \
        --format="value(nameServers)" | tr ';' '\n'

    echo
    echo "Steps to update at Squarespace:"
    echo "1. Log into your Squarespace account"
    echo "2. Go to Settings > Domains > alanshum.org"
    echo "3. Click 'Use Custom Nameservers'"
    echo "4. Replace current nameservers with the ones listed above"
    echo "5. Save changes"
    echo "6. Wait 24-48 hours for DNS propagation"
    echo
}

# Function to show verification commands
show_verification() {
    echo "=========================================="
    echo "Verification Commands (run after 24-48h)"
    echo "=========================================="
    echo
    echo "# Check DNS resolution:"
    echo "nslookup n8n.alanshum.org"
    echo "nslookup langflow.alanshum.org"
    echo
    echo "# Test services:"
    echo "curl -I https://n8n.alanshum.org/healthz"
    echo "curl -I https://api-server.alanshum.org/health"
    echo
    echo "# Check SSL certificates:"
    echo "kubectl get certificates -n automation"
    echo
}

# Main execution
main() {
    echo "=========================================="
    echo "Google Cloud DNS Setup for alanshum.org"
    echo "=========================================="
    echo
    
    print_status "Starting DNS setup process..."
    
    # Pre-flight checks
    check_auth
    check_project
    
    # Setup process
    enable_dns_api
    create_dns_zone
    create_dns_records
    
    # Post-setup information
    show_nameservers
    show_verification
    
    print_success "DNS setup completed successfully!"
    print_warning "Remember to update your domain registrar with the nameservers shown above"
}

# Run main function
main "$@"
