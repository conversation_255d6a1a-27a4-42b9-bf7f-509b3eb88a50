#!/bin/bash

# Deploy to Kubernetes Script
# This script deploys all manifests to your Kubernetes cluster

set -e

echo "Starting Kubernetes deployment..."

# Check if kubectl is available and cluster is accessible
if ! kubectl cluster-info &> /dev/null; then
    echo "❌ kubectl not available or cluster not accessible"
    echo "Please ensure you're connected to your Kubernetes cluster"
    exit 1
fi

echo "✅ Kubernetes cluster accessible"

# Apply manifests in order
echo "Applying namespaces..."
kubectl apply -f k8s-manifests/namespaces/
echo "✅ Namespaces applied"

echo "Applying secrets..."
kubectl apply -f k8s-manifests/secrets/
echo "✅ Secrets applied"

echo "Applying configmaps..."
kubectl apply -f k8s-manifests/configmaps/
echo "✅ ConfigMaps applied"

echo "Applying storage..."
kubectl apply -f k8s-manifests/storage/
echo "✅ Storage applied"

echo "Applying databases..."
kubectl apply -f k8s-manifests/databases/
echo "✅ Databases applied"

echo "Waiting for databases to be ready..."
kubectl wait --for=condition=ready pod -l app=postgres-n8n -n automation --timeout=300s
kubectl wait --for=condition=ready pod -l app=postgres-langflow -n automation --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n automation --timeout=300s
echo "✅ Databases ready"

echo "Applying services..."
kubectl apply -f k8s-manifests/services/
echo "✅ Services applied"

echo "Applying SSL certificates..."
kubectl apply -f k8s-manifests/ssl/
echo "✅ SSL certificates applied"

echo "Applying ingress..."
kubectl apply -f k8s-manifests/ingress/
echo "✅ Ingress applied"

echo "Applying autoscaling..."
kubectl apply -f k8s-manifests/autoscaling/
echo "✅ Autoscaling applied"

echo ""
echo "Deployment completed! Checking status..."
echo ""

# Check deployment status
echo "=== PODS ==="
kubectl get pods -n automation

echo ""
echo "=== SERVICES ==="
kubectl get services -n automation

echo ""
echo "=== INGRESS ==="
kubectl get ingress -n automation

echo ""
echo "=== HPA ==="
kubectl get hpa -n automation

echo ""
echo "Deployment completed successfully! 🎉"
