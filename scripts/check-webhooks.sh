#!/bin/bash

echo "=== GKE Webhook Health Check ==="
echo "This script checks all admission webhooks for endpoint availability"
echo

# Function to check service-based webhooks
check_service_webhook() {
    local webhook_name="$1"
    local namespace="$2"
    local service_name="$3"
    local port="$4"

    echo "  Webhook: $webhook_name"
    echo "  Service: $namespace/$service_name:$port"

    # Check if service exists
    if kubectl get service "$service_name" -n "$namespace" >/dev/null 2>&1; then
        echo "  ✓ Service exists"

        # Check endpoints
        endpoints=$(kubectl get endpoints "$service_name" -n "$namespace" -o jsonpath='{.subsets[*].addresses[*].ip}' 2>/dev/null)
        if [ -n "$endpoints" ]; then
            echo "  ✓ Endpoints available: $endpoints"

            # Check if pods are ready
            pod_count=$(kubectl get pods -n "$namespace" -l "app.kubernetes.io/name=$service_name" --field-selector=status.phase=Running 2>/dev/null | wc -l)
            if [ "$pod_count" -gt 1 ]; then
                echo "  ✓ Pods running: $((pod_count-1))"
            else
                # Try alternative label selectors
                pod_count=$(kubectl get pods -n "$namespace" -l "app=$service_name" --field-selector=status.phase=Running 2>/dev/null | wc -l)
                if [ "$pod_count" -gt 1 ]; then
                    echo "  ✓ Pods running: $((pod_count-1))"
                else
                    echo "  ⚠ Could not determine pod status"
                fi
            fi
            return 0
        else
            echo "  ✗ No endpoints available - WEBHOOK MAY BE DOWN"
            return 1
        fi
    else
        echo "  ✗ Service does not exist - WEBHOOK MISCONFIGURED"
        return 1
    fi
}

# Check validating webhooks
echo "=== Validating Webhook Configurations ==="
echo

webhook_issues=0

for webhook in $(kubectl get validatingwebhookconfigurations -o name); do
    webhook_name=$(echo $webhook | cut -d'/' -f2)
    echo "Checking webhook: $webhook_name"

    # Get webhook details
    webhook_config=$(kubectl get $webhook -o json)

    # Check service-based webhooks
    service_webhooks=$(echo "$webhook_config" | jq -r '.webhooks[] | select(.clientConfig.service != null) | "\(.name)|\(.clientConfig.service.namespace)|\(.clientConfig.service.name)|\(.clientConfig.service.port)"')

    if [ -n "$service_webhooks" ]; then
        echo "$service_webhooks" | while IFS='|' read -r webhook_detail namespace service_name port; do
            if [ -n "$webhook_detail" ]; then
                if ! check_service_webhook "$webhook_detail" "$namespace" "$service_name" "$port"; then
                    webhook_issues=$((webhook_issues + 1))
                fi
                echo
            fi
        done
    fi

    # Check URL-based webhooks
    url_webhooks=$(echo "$webhook_config" | jq -r '.webhooks[] | select(.clientConfig.url != null) | "\(.name): \(.clientConfig.url)"')
    if [ -n "$url_webhooks" ]; then
        echo "  URL-based webhooks (managed by GKE):"
        echo "$url_webhooks" | while read line; do
            echo "    $line"
        done
        echo "  ✓ URL-based webhooks are managed by GKE control plane"
        echo
    fi

    echo "---"
done

# Check mutating webhooks
echo
echo "=== Mutating Webhook Configurations ==="
echo

for webhook in $(kubectl get mutatingwebhookconfigurations -o name); do
    webhook_name=$(echo $webhook | cut -d'/' -f2)
    echo "Checking webhook: $webhook_name"

    # Get webhook details
    webhook_config=$(kubectl get $webhook -o json)

    # Check service-based webhooks
    service_webhooks=$(echo "$webhook_config" | jq -r '.webhooks[] | select(.clientConfig.service != null) | "\(.name)|\(.clientConfig.service.namespace)|\(.clientConfig.service.name)|\(.clientConfig.service.port)"')

    if [ -n "$service_webhooks" ]; then
        echo "$service_webhooks" | while IFS='|' read -r webhook_detail namespace service_name port; do
            if [ -n "$webhook_detail" ]; then
                if ! check_service_webhook "$webhook_detail" "$namespace" "$service_name" "$port"; then
                    webhook_issues=$((webhook_issues + 1))
                fi
                echo
            fi
        done
    fi

    # Check URL-based webhooks
    url_webhooks=$(echo "$webhook_config" | jq -r '.webhooks[] | select(.clientConfig.url != null) | "\(.name): \(.clientConfig.url)"')
    if [ -n "$url_webhooks" ]; then
        echo "  URL-based webhooks (managed by GKE):"
        echo "$url_webhooks" | while read line; do
            echo "    $line"
        done
        echo "  ✓ URL-based webhooks are managed by GKE control plane"
        echo
    fi

    echo "---"
done

echo
echo "=== Summary ==="
if [ "$webhook_issues" -eq 0 ]; then
    echo "✅ All admission webhooks are healthy and have available endpoints"
    echo "   No action required."
else
    echo "⚠️  Found $webhook_issues webhook(s) with endpoint issues"
    echo "   Consider restarting the affected webhook deployments:"
    echo "   kubectl rollout restart deployment <webhook-deployment> -n <namespace>"
fi

echo
echo "=== Webhook Restart Commands (if needed) ==="
echo "# Restart GMP operator webhook:"
echo "kubectl rollout restart deployment gmp-operator -n gmp-system"
echo
echo "# Restart cert-manager webhook:"
echo "kubectl rollout restart deployment cert-manager-webhook -n cert-manager"
echo
echo "# Restart ingress-nginx admission webhook:"
echo "kubectl rollout restart deployment ingress-nginx-controller -n ingress-nginx"
echo
echo "# Restart prometheus operator webhook:"
echo "kubectl rollout restart deployment prometheus-kube-prometheus-operator -n monitoring"
