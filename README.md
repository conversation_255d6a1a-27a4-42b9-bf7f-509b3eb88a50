# Automation Agent

This repository contains Docker services for automation workflows, including n8n, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> as a reverse proxy.

## Repository Structure

```
automation-agent/
├── docker-compose.yml        # Master compose file that includes all services
├── k8s-manage.sh             # Kubernetes management script
├── k8s-manifests/            # Kubernetes deployment manifests
│   ├── namespaces/           # Namespace definitions
│   ├── storage/              # Persistent Volume Claims
│   ├── secrets/              # Database credentials
│   ├── configmaps/           # Service configurations
│   ├── databases/            # PostgreSQL and Redis deployments
│   └── services/             # Application service deployments
├── n8n-workflows/            # n8n workflow backup system
│   ├── README.md             # Backup system documentation
│   ├── backups/              # Compressed backup archives
│   │   ├── daily/            # Daily automated backups
│   │   └── manual/           # Manual backup archives
│   └── exports/              # Raw exported workflow JSON files
├── scripts/                  # Automation and management scripts
│   ├── backup-n8n-workflows.sh    # n8n workflow backup script
│   ├── restore-n8n-workflows.sh   # n8n workflow restoration script
│   └── ...                   # Other utility scripts
├── traefik/                  # Traefik reverse proxy (legacy Docker Compose)
│   ├── docker-compose.yml    # Traefik Docker configuration
│   └── config/traefik.yml    # Traefik routing rules
├── n8n/                      # n8n workflow automation (legacy Docker Compose)
│   └── docker-compose.yml    # n8n Docker configuration
├── langflow/                 # Langflow service (legacy Docker Compose)
│   └── docker-compose.yml    # Langflow Docker configuration
├── portainer/                # Portainer container management (legacy Docker Compose)
│   └── docker-compose.yml    # Portainer Docker configuration
└── api-server/               # API Server service
    ├── docker-compose.yml    # API Server Docker configuration (legacy)
    ├── Dockerfile            # Container image definition
    └── src/                  # Source code
```

## Services

- **Traefik**: Reverse proxy that routes requests to the appropriate service
- **n8n**: Workflow automation tool
- **Langflow**: LangChain flow editor with a drag-and-drop interface
- **Portainer**: Web-based Docker management UI
- **API Server**: General purpose API server with Google OAuth and development mode by default

## n8n Workflow Backup System

This repository includes an automated backup system for your n8n workflows hosted on Google Cloud. The system provides:

- **Automated Daily Backups**: GitHub Actions workflow runs daily at 2 AM UTC
- **Manual Backups**: On-demand backup execution
- **Version Control**: All workflows tracked in Git with full history
- **Easy Restoration**: Scripts to restore individual or all workflows
- **Organized Storage**: Timestamped exports and compressed archives

### Quick Start

1. **Set up API Key**: Generate an API key in your n8n instance (Settings > API Keys)
2. **Configure Secrets**: Add `N8N_API_KEY` to your GitHub repository secrets
3. **Run Manual Backup**:
   ```bash
   export N8N_API_KEY="your-api-key"
   ./scripts/backup-n8n-workflows.sh
   ```

### Available Commands

```bash
# Backup workflows
./scripts/backup-n8n-workflows.sh --type daily
./scripts/backup-n8n-workflows.sh --type manual

# List available backups
./scripts/restore-n8n-workflows.sh --list

# Restore all workflows from a backup
./scripts/restore-n8n-workflows.sh --all 20240101_120000

# Restore single workflow
./scripts/restore-n8n-workflows.sh --single workflow.json
```

For detailed documentation, see [n8n-workflows/README.md](n8n-workflows/README.md).

## Managing Services

This project has been migrated to **Kubernetes (GKE)**. Use the provided Kubernetes management script:

```bash
# Check status of all services
./k8s-manage.sh status

# Deploy all services
./k8s-manage.sh deploy

# Restart all services
./k8s-manage.sh restart

# Stop application services (keeps databases)
./k8s-manage.sh stop

# Scale services up/down
./k8s-manage.sh scale 0  # Scale down
./k8s-manage.sh scale 1  # Scale up

# View logs for a specific service
./k8s-manage.sh logs api-server
```

### Direct kubectl Commands

You can also use kubectl directly:

```bash
# Check all pods
kubectl get pods -n automation

# Check services
kubectl get services -n automation

# View logs
kubectl logs -f deployment/api-server -n automation

# Restart a specific service
kubectl rollout restart deployment/api-server -n automation
```

## Accessing Services

Services are accessible through the NGINX Ingress Controller:

- **n8n**: https://n8n.alanshum.org
- **Langflow**: https://langflow.alanshum.org
- **Portainer**: https://portainer.alanshum.org
- **API Server**: https://api-server.alanshum.org

**Monitoring & Observability:**
- **Grafana**: https://grafana.alanshum.org (admin/prom-operator)
- **Prometheus**: https://prometheus.alanshum.org

**External IP**: `************` (LoadBalancer)

## 🔄 Domain Migration Options

Choose one of two migration paths:

### Option A: Cloudflare Tunnel (Quick Migration)
- Update existing tunnel to point to `https://************:443`
- Add `noTLSVerify: true` for self-signed certificates
- Keep current Cloudflare setup

### Option B: Google Cloud DNS (Production Setup)
- Migrate to native Google Cloud DNS
- Better SSL certificate support
- Run: `./scripts/setup-google-dns.sh`

See `CLOUDFLARE_TUNNEL_MIGRATION.md` for detailed instructions.

## Architecture

The services are deployed on **Google Kubernetes Engine (GKE)** with the following architecture:

- **Kubernetes Namespace**: `automation`
- **Database Services**: PostgreSQL (for n8n and Langflow) and Redis (for n8n queues)
- **Application Services**: n8n, Langflow, API Server, Portainer
- **Ingress**: NGINX Ingress Controller with LoadBalancer (External IP: `************`)
- **Networking**: Kubernetes ClusterIP services with ingress routing
- **Storage**: Persistent Volume Claims for data persistence

## Cloud Infrastructure

This setup runs on Google Cloud Platform:
- **GKE Cluster**: `automation-cluster` in `us-central1-a`
- **Container Registry**: `gcr.io/macmini-home-server`
- **External Access**: Configured for domain-based routing