# n8n Workflow Backup System

This directory contains an automated backup system for your n8n workflows hosted on Google Cloud. The system exports all workflows from your n8n instance and stores them in this repository with proper versioning and Git history.

## Directory Structure

```
n8n-workflows/
├── README.md                 # This file
├── backups/                  # Compressed backup archives
│   ├── daily/               # Daily automated backups
│   └── manual/              # Manual backup archives
└── exports/                 # Raw exported workflow JSON files
    └── YYYYMMDD_HHMMSS/     # Timestamped export directories
        ├── backup_info.json  # Backup metadata
        └── *.json           # Individual workflow files
```

## Prerequisites

Before using the backup system, ensure you have:

1. **jq** installed: `brew install jq` (for JSON processing)
2. **curl** installed (usually pre-installed on macOS/Linux)
3. **git** configured in your repository
4. **n8n API Key** from your n8n instance

## Setup

### 1. Generate n8n API Key

1. Log into your n8n instance at https://n8n.alanshum.org
2. Go to **Settings** > **API Keys**
3. Click **Create API Key**
4. Copy the generated key

### 2. Set Environment Variables

```bash
# Set your n8n API key (required)
export N8N_API_KEY="your-api-key-here"

# Optional: Override default n8n host
export N8N_HOST="https://n8n.alanshum.org"
```

For persistent configuration, add these to your shell profile (`~/.bashrc`, `~/.zshrc`, etc.):

```bash
echo 'export N8N_API_KEY="your-api-key-here"' >> ~/.zshrc
```

## Usage

### Manual Backup

Run a manual backup of all workflows:

```bash
# Basic manual backup
./scripts/backup-n8n-workflows.sh

# Manual backup with custom cleanup (keep 7 days)
./scripts/backup-n8n-workflows.sh --cleanup 7
```

### Daily Automated Backup

For automated daily backups:

```bash
# Run daily backup (typically used in cron or GitHub Actions)
./scripts/backup-n8n-workflows.sh --type daily
```

### Export Only (No Archive/Git)

To export workflows without creating archives or committing to git:

```bash
# Export workflows only
./scripts/backup-n8n-workflows.sh --no-git --no-archive
```

## Script Options

| Option | Description | Default |
|--------|-------------|---------|
| `-t, --type TYPE` | Backup type: 'daily' or 'manual' | manual |
| `-c, --cleanup DAYS` | Cleanup backups older than DAYS | 30 |
| `--no-git` | Skip git commit | false |
| `--no-archive` | Skip creating archive file | false |
| `-h, --help` | Show help message | - |

## Automated Scheduling

### Option 1: GitHub Actions (Recommended)

The repository includes a GitHub Actions workflow that runs daily backups automatically. See `.github/workflows/backup-n8n-workflows.yml`.

### Option 2: Cron Job

Set up a local cron job for automated backups:

```bash
# Edit crontab
crontab -e

# Add daily backup at 2 AM
0 2 * * * cd /path/to/automation-agent && ./scripts/backup-n8n-workflows.sh --type daily
```

## Backup Contents

Each backup includes:

1. **Individual Workflow Files**: Each workflow exported as a separate JSON file
2. **Backup Metadata**: Information about the backup (timestamp, count, etc.)
3. **Compressed Archive**: Tar.gz file for easy storage and transfer
4. **Git History**: All changes tracked in version control

### Workflow File Naming

Workflow files are named using the pattern:
```
{sanitized_workflow_name}_{workflow_id}.json
```

Example: `My_Data_Processing_Workflow_123.json`

## Restoration

### Restore Individual Workflow

1. Navigate to the desired backup in `exports/YYYYMMDD_HHMMSS/`
2. Find the workflow JSON file
3. In n8n, go to **Workflows** > **Import from File**
4. Upload the JSON file

### Restore from Archive

1. Extract the desired archive from `backups/daily/` or `backups/manual/`
2. Follow the individual workflow restoration process

### Bulk Restoration

For restoring multiple workflows, you can use the n8n CLI or API:

```bash
# Using n8n CLI (if available)
n8n import:workflow --input=path/to/workflow.json

# Using API (requires additional scripting)
curl -X POST -H "X-N8N-API-KEY: $N8N_API_KEY" \
     -H "Content-Type: application/json" \
     -d @workflow.json \
     "$N8N_HOST/api/v1/workflows"
```

## Monitoring and Troubleshooting

### Check Backup Status

```bash
# View recent backups
ls -la n8n-workflows/backups/daily/
ls -la n8n-workflows/exports/

# Check backup metadata
cat n8n-workflows/exports/YYYYMMDD_HHMMSS/backup_info.json
```

### Common Issues

1. **API Key Error**: Ensure `N8N_API_KEY` is set and valid
2. **Connection Error**: Verify `N8N_HOST` is accessible
3. **Permission Error**: Ensure script has execute permissions (`chmod +x`)
4. **Git Error**: Ensure you're in a git repository with proper configuration

### Logs

The backup script provides colored output:
- 🔵 **INFO**: General information
- 🟢 **SUCCESS**: Successful operations
- 🟡 **WARNING**: Non-critical issues
- 🔴 **ERROR**: Critical errors that stop execution

## Security Considerations

1. **API Key Protection**: Never commit your API key to the repository
2. **Environment Variables**: Use environment variables or secure secret management
3. **Access Control**: Ensure backup files have appropriate permissions
4. **Network Security**: Use HTTPS for all API communications

## Backup Retention

- **Daily Backups**: Kept for 30 days by default
- **Manual Backups**: Kept indefinitely (manual cleanup)
- **Git History**: Permanent record of all changes

Adjust retention with the `--cleanup` option:

```bash
# Keep daily backups for 7 days
./scripts/backup-n8n-workflows.sh --type daily --cleanup 7
```

## Integration with CI/CD

The backup system is designed to work with:
- GitHub Actions (included)
- GitLab CI/CD
- Jenkins
- Any system that can run bash scripts

See the GitHub Actions workflow for an example of automated scheduling and secret management.
