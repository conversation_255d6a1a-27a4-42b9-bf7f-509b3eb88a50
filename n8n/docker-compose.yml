networks:
  traefik-network:
    external: true

services:
  # main n8n node
  n8n:
    image: n8nio/n8n
    # Remove direct port exposure as Traefik will handle this
    # ports:
    #   - "5678:5678"
    environment:
      - WEBHOOK_URL=https://n8n.alanshum.org/
      - N8N_PROTOCOL=https
      - N8N_HOST=n8n.alanshum.org
      - N8N_PORT=5678
      - N8N_ENCRYPTION_KEY=YrqHt5C/Kw0nQYIFWvP/aYYg2OeT2Ruu # Must match main instance!

      # Redis configuration for n8n queue mode and caching
      - EXECUTIONS_MODE=queue
      - QUEUE_BULL_REDIS_HOST=redis
      - QUEUE_BULL_REDIS_PORT=6379
      - QUEUE_BULL_REDIS_PASSWORD=tok-seldom-TRENDY-events
      - QUEUE_BULL_REDIS_DB=0

      # Optional: Redis for session storage
      - N8N_REDIS_HOST=redis
      - N8N_REDIS_PORT=6379
      - N8N_REDIS_PASSWORD=tok-seldom-TRENDY-events
    volumes:
      - n8n-data:/home/<USER>/.n8n # using sqlite to store workflow execution data
    networks:
      - traefik-network
      - default
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  # n8n worker that actually executes the workflow
  n8n-worker:
    image: n8nio/n8n
    restart: always
    command: worker
    environment:
      - N8N_ENCRYPTION_KEY=YrqHt5C/Kw0nQYIFWvP/aYYg2OeT2Ruu # Must match main instance!

      # Redis configuration for n8n queue mode and caching
      - EXECUTIONS_MODE=queue
      - QUEUE_BULL_REDIS_HOST=redis
      - QUEUE_BULL_REDIS_PORT=6379
      - QUEUE_BULL_REDIS_PASSWORD=tok-seldom-TRENDY-events
      - QUEUE_BULL_REDIS_DB=0
    volumes:
      - n8n-data:/home/<USER>/.n8n # using sqlite to store workflow execution data
    networks:
      - default
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  # queue
  redis:
    image: redis:alpine
    restart: always
    command: redis-server --requirepass tok-seldom-TRENDY-events
    networks:
      - default
    volumes:
      - n8n-redis:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 10

  # chat memory
  postgres:
    image: postgres:latest
    environment:
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n
      - POSTGRES_DB=n8n
    volumes:
      - ./init-indexes.sql:/docker-entrypoint-initdb.d/init-indexes.sql
      - n8n-postgres:/var/lib/postgresql/data
    networks:
      default:
        aliases:
          - postgres
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n -d n8n"]
      interval: 5s
      timeout: 5s
      retries: 10

volumes:
  n8n-postgres:
  n8n-redis:
  n8n-data: