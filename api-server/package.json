{"name": "api-server", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only --ignore-watch node_modules --no-notify src/index.ts", "docker": "docker-compose up", "docker:build": "docker-compose build"}, "dependencies": {"@fastify/cookie": "^9.2.0", "@fastify/env": "^4.3.0", "@fastify/oauth2": "^7.5.0", "@fastify/session": "^10.5.0", "dotenv": "^16.3.1", "fastify": "^4.24.3"}, "devDependencies": {"@types/node": "^20.8.10", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}}