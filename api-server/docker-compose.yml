# Simple development configuration with live reloading
version: '3.8'

services:
  api-server:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ./:/app
      - /app/node_modules
    environment:
      - SERVER_PORT=3001
      - NODE_ENV=development
    ports:
      - "3001:3001" # Expose the API port locally
      - "9229:9229" # For debugging
    networks:
      - traefik-network
      - default
    command: npm run dev
    # Always restart the container if it stops
    restart: unless-stopped