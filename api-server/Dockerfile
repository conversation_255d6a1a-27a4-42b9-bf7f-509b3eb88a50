FROM node:slim

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm install

# Copy source code
COPY . .

# Create .env file from example if it doesn't exist
RUN if [ ! -f .env ]; then cp .env.example .env; fi

# Expose the port specified in environment variable or default to 3001
EXPOSE 3001
# Expose debugging port
EXPOSE 9229

# Use development mode with live reloading
CMD ["npm", "run", "dev"]