# API Server with Google OAuth

This is a Fastify-based API server that includes Google OAuth authentication.

## Features

- Google OAuth authentication
- Session management
- Environment variable configuration
- Fastify framework

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Create a `.env` file based on `.env.example`:
   ```bash
   cp .env.example .env
   ```

3. Configure Google OAuth:
   - Go to the [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select an existing one
   - Navigate to "APIs & Services" > "Credentials"
   - Create an OAuth 2.0 Client ID
   - Add `https://api-server.persoack.org/auth/google/callback` as an authorized redirect URI
   - Copy the Client ID and Client Secret to your `.env` file

4. Update the `.env` file with your configuration:
   ```
   SERVER_PORT=3001
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   OAUTH_CALLBACK_URL=https://api-server.persoack.org/auth/google/callback
   SESSION_SECRET=your_session_secret_key_change_this_in_production
   ```

5. Build and start the server:
   ```bash
   npm run build
   npm start
   ```

## API Routes

- `GET /` - Home route, returns a simple message
- `GET /auth/google` - Initiates Google OAuth flow
- `GET /auth/google/callback` - Callback for Google OAuth
- `GET /profile` - Returns user profile if authenticated
- `GET /auth/logout` - Logs out the user
- `GET /auth/error` - Error page for authentication failures

## Development

### Docker Development (Recommended)

```bash
npm run docker
```

This starts the server in a Docker container with live reloading. Your local files are mounted into the container, so any changes you make will be immediately reflected.

### Local Development

If you prefer to run the server directly on your machine without Docker:

```bash
# Install dependencies
npm install

# Start the development server
npm run dev
```

This uses ts-node-dev with optimized settings for fast reloading.

### Debugging

The Docker setup exposes port 9229 for debugging. You can attach a debugger to this port.

In VS Code, you can create a launch configuration like this:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "attach",
      "name": "Attach to Docker",
      "port": 9229,
      "restart": true,
      "remoteRoot": "/app",
      "localRoot": "${workspaceFolder}"
    }
  ]
}
```

## Quick Start

The API server is configured for development with live reloading.

### Start the Server

Simply run:

```bash
npm run docker
```

This will start the server with:
- Live reloading when you change files in src/
- Source code mounted from your local directory
- Console output for debugging
- API port (3001) and debugging port (9229) exposed locally

To access the API server:
- Locally: http://localhost:3001
- Via Traefik: https://api-server.persoack.org

## Docker Configuration

The Docker setup is optimized for development:

- `docker-compose.yml` - Simple configuration with live reloading
- `Dockerfile` - Development-focused with live reloading

The configuration mounts your local source code into the container, so any changes you make to the code will be immediately reflected in the running application.
