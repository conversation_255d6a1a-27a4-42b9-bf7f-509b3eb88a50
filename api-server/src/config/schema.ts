export const schema = {
  type: 'object',
  required: ['SERVER_PORT', 'GOOGLE_CLIENT_ID', 'GOOGLE_CLIENT_SECRET', 'OAUTH_CALLBACK_URL', 'SESSION_SECRET'],
  properties: {
    SERVER_PORT: {
      type: 'string',
      default: '3001'
    },
    GOOGLE_CLIENT_ID: {
      type: 'string'
    },
    GOOGLE_CLIENT_SECRET: {
      type: 'string'
    },
    OAUTH_CALLBACK_URL: {
      type: 'string'
    },
    SESSION_SECRET: {
      type: 'string'
    }
  }
};

export type ConfigSchema = {
  SERVER_PORT: string;
  GOOGLE_CLIENT_ID: string;
  GOOGLE_CLIENT_SECRET: string;
  OAUTH_CALLBACK_URL: string;
  SESSION_SECRET: string;
};
