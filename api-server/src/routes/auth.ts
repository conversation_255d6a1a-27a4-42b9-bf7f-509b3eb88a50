import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { ConfigSchema } from '../config/schema';

interface OAuthUser {
  id: string;
  email: string;
  name: string;
  picture: string;
}

declare module 'fastify' {
  interface Session {
    user?: OAuthUser;
    isAuthenticated: boolean;
  }
}

export default async function authRoutes(fastify: FastifyInstance) {
  // Note: The '/auth/google' route is automatically created by the OAuth2 plugin
  // We don't need to define it here as it's configured in index.ts with startRedirectPath

  // Google OAuth callback route
  fastify.get('/oidc/callback', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { token } = await fastify.oauth2.getAccessTokenFromAuthorizationCodeFlow(request);

      // Fetch user profile from Google
      const userInfoResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          Authorization: `Bearer ${token.access_token}`
        }
      });

      if (!userInfoResponse.ok) {
        throw new Error('Failed to fetch user info from Google');
      }

      const userInfo = await userInfoResponse.json() as OAuthUser;

      // Store user in session
      if (request.session) {
        request.session.user = userInfo;
        request.session.isAuthenticated = true;
      }

      // Redirect to profile page or home page
      reply.redirect('/profile');
    } catch (error) {
      fastify.log.error(error);
      reply.redirect('/auth/error');
    }
  });

  // Profile route to verify authentication
  fastify.get('/profile', async (request: FastifyRequest, _reply: FastifyReply) => {
    if (request.session && request.session.isAuthenticated && request.session.user) {
      return {
        authenticated: true,
        user: request.session.user
      };
    }

    return {
      authenticated: false,
      message: 'Not authenticated'
    };
  });

  // Logout route
  fastify.get('/auth/logout', async (request: FastifyRequest, _reply: FastifyReply) => {
    if (request.session) {
      request.session.destroy();
    }

    return { success: true, message: 'Logged out successfully' };
  });

  // Auth error route
  fastify.get('/auth/error', async (_request: FastifyRequest, _reply: FastifyReply) => {
    return {
      error: true,
      message: 'Authentication failed'
    };
  });
}
