import fastify, { FastifyRequest, FastifyReply } from 'fastify';
import fastifyEnv from '@fastify/env';
import fastifyOauth2 from '@fastify/oauth2';
import fastifyCookie from '@fastify/cookie';
import fastifySession from '@fastify/session';
import { schema, ConfigSchema } from './config/schema';
import authRoutes from './routes/auth';
import { join } from 'path';
import { config } from 'dotenv';

// Load environment variables from .env file
config({ path: join(__dirname, '../.env') });

const server = fastify({
  logger: true
});

// Register environment variables plugin
async function setupServer() {
  await server.register(fastifyEnv, {
    schema,
    dotenv: true,
    data: process.env
  });

  // Type assertion for config
  const config = server.config as ConfigSchema;

  /* ========================================================================== */
  /*                                 AUTH ROUTES                                */
  /* ========================================================================== */
  // Register cookie plugin
  await server.register(fastifyCookie);

  // Register session plugin
  await server.register(fastifySession, {
    cookieName: 'sessionId',
    secret: config.SESSION_SECRET,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: 7 * 24 * 60 * 60 * 1000 // 1 week
    }
  });

  // Register OAuth2 plugin
  await server.register(fastifyOauth2, {
    name: 'oauth2',
    scope: ['profile', 'email'],
    credentials: {
      client: {
        id: config.GOOGLE_CLIENT_ID,
        secret: config.GOOGLE_CLIENT_SECRET
      },
      auth: {
        authorizeHost: 'https://accounts.google.com',
        authorizePath: '/o/oauth2/v2/auth',
        tokenHost: 'https://www.googleapis.com',
        tokenPath: '/oauth2/v4/token'
      }
    },
    // This automatically creates the /auth/google route
    startRedirectPath: '/auth/google',
    callbackUri: config.OAUTH_CALLBACK_URL
  });


  // Register auth routes
  await server.register(authRoutes);

  /* ========================================================================== */
  /*                                 MAIN ROUTES                                */
  /* ========================================================================== */
  server.get('/', async (_request: FastifyRequest, _reply: FastifyReply) => {
    return { message: 'API server running' };
  });

  // Health check endpoint for Kubernetes
  server.get('/health', async (_request: FastifyRequest, _reply: FastifyReply) => {
    return { status: 'ok', timestamp: new Date().toISOString() };
  });

  return server;
}

const start = async () => {
  try {
    const server = await setupServer();
    const config = server.config as ConfigSchema;
    const port = parseInt(config.SERVER_PORT);

    await server.listen({ port, host: '0.0.0.0' });
    server.log.info(`Server listening on port ${port}`);
  } catch (err) {
    console.error(err);
    process.exit(1);
  }
};

start();