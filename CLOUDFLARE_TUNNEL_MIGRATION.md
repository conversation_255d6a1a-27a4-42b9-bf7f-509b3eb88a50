# Domain Migration Guide: Google Cloud DNS Setup for alanshum.org

## Overview

This guide helps you set up Google Cloud DNS for your new domain `alanshum.org` registered at Squarespace, pointing to your GKE cluster.

## New Setup

```
subdomain.alanshum.org → Google Cloud DNS → GKE LoadBalancer (************)
```

**Services to be configured:**
- n8n.alanshum.org
- langflow.alanshum.org
- api-server.alanshum.org
- portainer.alanshum.org
- grafana.alanshum.org
- prometheus.alanshum.org

---

## 🚀 OPTION A: Cloudflare Tunnel Migration (Quick)

### Step 1: Identify Current Tunnel Configuration

1. **Log into Cloudflare Dashboard**
   - Go to https://dash.cloudflare.com
   - Select your domain `persoack.org`
   - Navigate to **Zero Trust** → **Access** → **Tunnels**

2. **Find Your Existing Tunnel**
   - Look for the tunnel that's currently routing your subdomains
   - Note the tunnel name/ID

### Step 2: Update Tunnel Configuration

#### Option A: Via Cloudflare Dashboard (Recommended)

1. **Edit Tunnel Routes**
   - Click on your tunnel name
   - Go to **Public Hostnames** tab
   - For each subdomain, update the service configuration:

   **Current Configuration:**
   ```
   Subdomain: n8n
   Domain: persoack.org
   Service: http://192.168.x.x:port (your home server)
   ```

   **New Configuration:**
   ```
   Subdomain: n8n
   Domain: persoack.org
   Service: http://************:80
   Additional Headers:
     Host: n8n.persoack.org
   ```

2. **Update All Subdomains:**

   | Subdomain | Service URL | Host Header | Additional Settings |
   |-----------|-------------|-------------|-------------------|
   | `n8n` | `https://************:443` | `n8n.persoack.org` | No TLS Verify: Yes |
   | `langflow` | `https://************:443` | `langflow.persoack.org` | No TLS Verify: Yes |
   | `api-server` | `https://************:443` | `api-server.persoack.org` | No TLS Verify: Yes |
   | `portainer` | `https://************:443` | `portainer.persoack.org` | No TLS Verify: Yes |
   | `grafana` | `https://************:443` | `grafana.persoack.org` | No TLS Verify: Yes |
   | `prometheus` | `https://************:443` | `prometheus.persoack.org` | No TLS Verify: Yes |

#### Option B: Via Configuration File

If you're using a config file (`config.yml`), update it:

```yaml
tunnel: your-tunnel-id
credentials-file: /path/to/credentials.json

ingress:
  # Application services
  - hostname: n8n.alanshum.org
    service: https://************:443
    originRequest:
      httpHostHeader: n8n.alanshum.org
      noTLSVerify: true  # Required for self-signed certs

  - hostname: langflow.alanshum.org
    service: https://************:443
    originRequest:
      httpHostHeader: langflow.alanshum.org
      noTLSVerify: true

  - hostname: api-server.alanshum.org
    service: https://************:443
    originRequest:
      httpHostHeader: api-server.alanshum.org
      noTLSVerify: true

  - hostname: portainer.alanshum.org
    service: https://************:443
    originRequest:
      httpHostHeader: portainer.alanshum.org
      noTLSVerify: true

  # Monitoring services
  - hostname: grafana.alanshum.org
    service: https://************:443
    originRequest:
      httpHostHeader: grafana.alanshum.org
      noTLSVerify: true

  - hostname: prometheus.alanshum.org
    service: https://************:443
    originRequest:
      httpHostHeader: prometheus.alanshum.org
      noTLSVerify: true

  # Catch-all rule (required)
  - service: http_status:404
```

### Step 3: Test the Migration

1. **Verify DNS Resolution**
   ```bash
   # Check that domains still resolve to Cloudflare
   nslookup n8n.alanshum.org
   nslookup langflow.alanshum.org
   nslookup api-server.alanshum.org
   nslookup portainer.alanshum.org
   ```

2. **Test Service Accessibility**
   ```bash
   # Test each service
   curl -I https://n8n.alanshum.org/healthz
   curl -I https://langflow.alanshum.org/health
   curl -I https://api-server.alanshum.org/health
   curl -I https://portainer.alanshum.org/
   ```

3. **Verify in Browser**
   - Open each URL in your browser
   - Confirm services load correctly
   - Check that SSL certificates are working (Cloudflare handles this)

### Step 4: Monitor and Troubleshoot

1. **Check Cloudflare Analytics**
   - Monitor traffic in Cloudflare dashboard
   - Look for any 5xx errors that might indicate connectivity issues

2. **Check GKE LoadBalancer**
   ```bash
   # Verify LoadBalancer is healthy
   kubectl get service --namespace ingress-nginx ingress-nginx-controller

   # Check ingress status
   kubectl get ingress -n automation
   ```

3. **Test Direct LoadBalancer Access**
   ```bash
   # Test without Cloudflare (for debugging)
   curl -H "Host: n8n.alanshum.org" http://************/healthz
   ```

## Troubleshooting Cloudflare Tunnel

### Issue: HTTP 530 Errors

If you're getting HTTP 530 errors, it's likely because:

1. **Tunnel is using HTTP but LoadBalancer requires HTTPS**
   ```bash
   # Test direct LoadBalancer access
   curl -H "Host: api-server.alanshum.org" http://************/health
   # Returns: 308 Permanent Redirect to HTTPS
   ```

2. **Solution**: Update tunnel to use HTTPS (see configuration above)

### Issue: SSL Certificate Errors

If tunnel can't connect due to SSL errors:

1. **Add noTLSVerify: true** in tunnel configuration
2. **Or disable SSL redirect temporarily**:
   ```bash
   # Remove SSL redirect from ingress (temporary)
   kubectl patch ingress automation-ingress -n automation -p '{"metadata":{"annotations":{"nginx.ingress.kubernetes.io/ssl-redirect":"false"}}}'

   # Re-enable after fixing tunnel
   kubectl patch ingress automation-ingress -n automation -p '{"metadata":{"annotations":{"nginx.ingress.kubernetes.io/ssl-redirect":"true"}}}'
   ```

### Verification Commands

```bash
# Test LoadBalancer directly (should work)
curl -k -H "Host: api-server.persoack.org" https://************/health

# Test via Cloudflare (should work after tunnel update)
curl -I https://api-server.persoack.org/health

# Check tunnel logs
cloudflared tunnel logs your-tunnel-name
```

## Important Notes

### SSL/TLS Considerations

**Important Update**: Your GKE LoadBalancer redirects HTTP to HTTPS, so the tunnel configuration must use HTTPS.

- **Cloudflare handles SSL termination** for your domains (user ↔ Cloudflare)
- **GKE LoadBalancer requires HTTPS** (Cloudflare ↔ GKE) due to SSL redirect
- **noTLSVerify: true** is required because GKE uses self-signed certificates
- This setup provides end-to-end encryption: User → Cloudflare (SSL) → GKE (SSL)

**Why HTTPS is Required:**
1. GKE ingress has SSL redirect enabled (`nginx.ingress.kubernetes.io/ssl-redirect: "true"`)
2. HTTP requests to LoadBalancer get 308 redirects to HTTPS
3. Cloudflare tunnel must follow these redirects by using HTTPS directly

### Host Headers
- **Critical**: Always set the `Host` header in Cloudflare tunnel configuration
- Without proper host headers, the NGINX ingress won't route requests correctly
- Each subdomain must have its corresponding host header

### Firewall Considerations
- GKE LoadBalancer is publicly accessible on the internet
- Cloudflare will connect directly to `************:80`
- No additional firewall rules needed in GCP (LoadBalancer handles this)

## Rollback Plan

If you need to rollback to your home server:

1. **Keep Home Server Running** (temporarily during migration)
2. **Update Cloudflare tunnel back to home server IPs**
3. **Test that home server services are still working**

## Benefits After Migration

1. **Better Uptime**: GKE cluster vs home server reliability
2. **Scalability**: Can scale services independently
3. **Professional Infrastructure**: Managed Kubernetes environment
4. **Better Monitoring**: GCP monitoring and logging
5. **Disaster Recovery**: Easier backup and restore procedures

## Next Steps

After successful migration:
1. **Monitor for 24-48 hours** to ensure stability
2. **Shut down home server services** (once confident)
3. **Update any hardcoded URLs** in your applications
4. **Set up GCP monitoring and alerting**
5. **Configure SSL certificates in GKE** (Phase 6) for direct access without Cloudflare

---

## 🌟 OPTION B: Google Cloud DNS Migration (Recommended)

### Why Choose Google Cloud DNS?

**Advantages:**
- ✅ **Native GCP Integration**: Seamless integration with GKE and other GCP services
- ✅ **Better Performance**: Direct routing without tunnel overhead
- ✅ **SSL Certificates**: Let's Encrypt certificates work properly
- ✅ **Professional Setup**: Industry-standard DNS management
- ✅ **Cost Effective**: No tunnel costs, pay only for DNS queries
- ✅ **Better Monitoring**: Full visibility in GCP console
- ✅ **Easier Automation**: Infrastructure as Code with Terraform/gcloud

**Considerations:**
- ⚠️ **Domain Transfer**: Need to update nameservers at domain registrar
- ⚠️ **DNS Propagation**: 24-48 hours for full propagation
- ⚠️ **Learning Curve**: Need to learn Google Cloud DNS management

### Prerequisites

1. **Domain Ownership**: You must own `persoack.org` domain
2. **Access to Domain Registrar**: To update nameservers
3. **GCP Project**: Your existing `macmini-home-server` project

### Step 1: Create Cloud DNS Zone

```bash
# Create a managed DNS zone for your domain
gcloud dns managed-zones create persoack-org-zone \
    --description="DNS zone for persoack.org" \
    --dns-name="persoack.org." \
    --project=macmini-home-server

# Get the nameservers assigned to your zone
gcloud dns managed-zones describe persoack-org-zone \
    --project=macmini-home-server \
    --format="value(nameServers)"
```

### Step 2: Create DNS Records

```bash
# Create A records for each subdomain pointing to LoadBalancer IP
gcloud dns record-sets transaction start \
    --zone=persoack-org-zone \
    --project=macmini-home-server

# Add A records for each service
gcloud dns record-sets transaction add ************ \
    --name=n8n.persoack.org. \
    --ttl=300 \
    --type=A \
    --zone=persoack-org-zone \
    --project=macmini-home-server

gcloud dns record-sets transaction add ************ \
    --name=langflow.persoack.org. \
    --ttl=300 \
    --type=A \
    --zone=persoack-org-zone \
    --project=macmini-home-server

gcloud dns record-sets transaction add ************ \
    --name=api-server.persoack.org. \
    --ttl=300 \
    --type=A \
    --zone=persoack-org-zone \
    --project=macmini-home-server

gcloud dns record-sets transaction add ************ \
    --name=portainer.persoack.org. \
    --ttl=300 \
    --type=A \
    --zone=persoack-org-zone \
    --project=macmini-home-server

gcloud dns record-sets transaction add ************ \
    --name=grafana.persoack.org. \
    --ttl=300 \
    --type=A \
    --zone=persoack-org-zone \
    --project=macmini-home-server

gcloud dns record-sets transaction add ************ \
    --name=prometheus.persoack.org. \
    --ttl=300 \
    --type=A \
    --zone=persoack-org-zone \
    --project=macmini-home-server

# Execute the transaction
gcloud dns record-sets transaction execute \
    --zone=persoack-org-zone \
    --project=macmini-home-server
```

### Step 3: Update Domain Registrar

1. **Get Google Cloud DNS Nameservers**:
   ```bash
   gcloud dns managed-zones describe persoack-org-zone \
       --project=macmini-home-server \
       --format="value(nameServers)"
   ```

2. **Update at Domain Registrar**:
   - Log into your domain registrar (where you bought persoack.org)
   - Find DNS/Nameserver settings
   - Replace current nameservers with Google Cloud DNS nameservers
   - Save changes

3. **Example Nameservers** (yours will be different):
   ```
   ns-cloud-a1.googledomains.com.
   ns-cloud-a2.googledomains.com.
   ns-cloud-a3.googledomains.com.
   ns-cloud-a4.googledomains.com.
   ```

### Step 4: Verify DNS Propagation

```bash
# Check if DNS is resolving correctly
nslookup n8n.persoack.org
nslookup langflow.persoack.org
nslookup api-server.persoack.org

# Check from different DNS servers
nslookup n8n.persoack.org 8.8.8.8
nslookup n8n.persoack.org 1.1.1.1

# Use dig for more detailed information
dig n8n.persoack.org
```

### Step 5: Test Services

```bash
# Test each service (after DNS propagation)
curl -I https://n8n.persoack.org/healthz
curl -I https://langflow.persoack.org/health
curl -I https://api-server.persoack.org/health
curl -I https://portainer.persoack.org/
curl -I https://grafana.persoack.org/
curl -I https://prometheus.persoack.org/
```

### Step 6: Disable Cloudflare Tunnel (Optional)

Once Google Cloud DNS is working:

1. **Stop Cloudflare Tunnel**:
   ```bash
   # On your home server or wherever tunnel runs
   cloudflared tunnel stop
   ```

2. **Remove Tunnel Configuration**:
   - Delete tunnel from Cloudflare dashboard
   - Remove tunnel configuration files

### Migration Timeline

| Phase | Duration | Description |
|-------|----------|-------------|
| **Setup** | 15 minutes | Create DNS zone and records |
| **Nameserver Update** | 5 minutes | Update at domain registrar |
| **DNS Propagation** | 24-48 hours | Global DNS propagation |
| **SSL Certificate** | 10 minutes | Let's Encrypt validation |
| **Testing** | 30 minutes | Verify all services |

### Troubleshooting Google Cloud DNS

1. **DNS Not Resolving**:
   ```bash
   # Check if nameservers are updated
   dig NS persoack.org

   # Check specific record
   dig n8n.persoack.org @ns-cloud-a1.googledomains.com
   ```

2. **SSL Certificate Issues**:
   ```bash
   # Check certificate status
   kubectl get certificates -n automation
   kubectl describe certificate automation-tls -n automation
   ```

3. **LoadBalancer Not Responding**:
   ```bash
   # Test direct LoadBalancer access
   curl -H "Host: n8n.persoack.org" http://************/healthz
   ```

### Cost Comparison

| Service | Monthly Cost (Estimate) |
|---------|-------------------------|
| **Cloudflare Tunnel** | $0 (Free tier) |
| **Google Cloud DNS** | $0.50 (for 1M queries) |
| **GKE LoadBalancer** | $18 (already using) |

### Automation Script

Create a script to automate the DNS setup:

```bash
#!/bin/bash
# dns-setup.sh

PROJECT_ID="macmini-home-server"
ZONE_NAME="persoack-org-zone"
DOMAIN="persoack.org"
LOADBALANCER_IP="************"

# Create DNS zone
gcloud dns managed-zones create $ZONE_NAME \
    --description="DNS zone for $DOMAIN" \
    --dns-name="$DOMAIN." \
    --project=$PROJECT_ID

# Create all A records
SUBDOMAINS=("n8n" "langflow" "api-server" "portainer" "grafana" "prometheus")

gcloud dns record-sets transaction start \
    --zone=$ZONE_NAME \
    --project=$PROJECT_ID

for subdomain in "${SUBDOMAINS[@]}"; do
    gcloud dns record-sets transaction add $LOADBALANCER_IP \
        --name="$subdomain.$DOMAIN." \
        --ttl=300 \
        --type=A \
        --zone=$ZONE_NAME \
        --project=$PROJECT_ID
done

gcloud dns record-sets transaction execute \
    --zone=$ZONE_NAME \
    --project=$PROJECT_ID

echo "DNS zone created. Update your domain registrar with these nameservers:"
gcloud dns managed-zones describe $ZONE_NAME \
    --project=$PROJECT_ID \
    --format="value(nameServers)"
```

## 🎯 Recommendation

**For Production Use**: Choose **Option B (Google Cloud DNS)** because:

1. **Better SSL Support**: Let's Encrypt certificates work properly
2. **Professional Setup**: Industry-standard DNS management
3. **Better Performance**: Direct routing without tunnel overhead
4. **Native GCP Integration**: Seamless with your existing infrastructure
5. **Future-Proof**: Easier to manage and automate

**For Quick Testing**: Use **Option A (Cloudflare Tunnel)** to verify services work, then migrate to Google Cloud DNS for production.
