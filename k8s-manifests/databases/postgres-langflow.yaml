apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-langflow
  namespace: automation
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres-langflow
  template:
    metadata:
      labels:
        app: postgres-langflow
    spec:
      containers:
      - name: postgres
        image: postgres:15
        env:
        - name: POSTGRES_USER
          value: "langflow"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: langflow-db-password
        - name: POSTGRES_DB
          value: "langflow"
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
          subPath: pgdata
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - langflow
            - -d
            - langflow
          initialDelaySeconds: 30
          periodSeconds: 5
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - langflow
            - -d
            - langflow
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-langflow-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-langflow-service
  namespace: automation
spec:
  selector:
    app: postgres-langflow
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP
