apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-n8n
  namespace: automation
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres-n8n
  template:
    metadata:
      labels:
        app: postgres-n8n
    spec:
      containers:
      - name: postgres
        image: postgres:15
        env:
        - name: POSTGRES_USER
          value: "n8n"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: n8n-db-password
        - name: POSTGRES_DB
          value: "n8n"
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
          subPath: pgdata
        - name: init-script
          mountPath: /docker-entrypoint-initdb.d/
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - n8n
            - -d
            - n8n
          initialDelaySeconds: 30
          periodSeconds: 5
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - n8n
            - -d
            - n8n
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-n8n-pvc
      - name: init-script
        configMap:
          name: n8n-init-sql

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-n8n-service
  namespace: automation
spec:
  selector:
    app: postgres-n8n
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP
