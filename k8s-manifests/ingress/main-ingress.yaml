apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: automation-ingress
  namespace: automation
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - n8n.alanshum.org
    - langflow.alanshum.org
    - api-server.alanshum.org

    secretName: automation-tls
  rules:
  - host: n8n.alanshum.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: n8n-service
            port:
              number: 5678
  - host: langflow.alanshum.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: langflow-service
            port:
              number: 7860
  - host: api-server.alanshum.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-server-service
            port:
              number: 3001

