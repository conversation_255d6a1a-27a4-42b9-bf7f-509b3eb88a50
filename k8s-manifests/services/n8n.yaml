apiVersion: apps/v1
kind: Deployment
metadata:
  name: n8n
  namespace: automation
spec:
  replicas: 1
  selector:
    matchLabels:
      app: n8n
  template:
    metadata:
      labels:
        app: n8n
    spec:
      securityContext:
        fsGroup: 1000
      containers:
      - name: n8n
        image: n8nio/n8n:latest
        securityContext:
          runAsUser: 1000
          runAsGroup: 1000
        envFrom:
        - configMapRef:
            name: n8n-config
        env:
        - name: N8N_ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: n8n-encryption-key
        - name: QUEUE_BULL_REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: redis-password
        - name: N8N_REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: redis-password
        - name: DB_POSTGRESDB_HOST
          value: "postgres-n8n-service"
        - name: DB_POSTGRESDB_PORT
          value: "5432"
        - name: DB_POSTGRESDB_DATABASE
          value: "n8n"
        - name: DB_POSTGRESDB_USER
          value: "n8n"
        - name: DB_POSTGRESDB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: n8n-db-password
        - name: DB_TYPE
          value: "postgresdb"
        ports:
        - containerPort: 5678
        volumeMounts:
        - name: n8n-data
          mountPath: /home/<USER>/.n8n
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 5678
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /healthz
            port: 5678
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: n8n-data
        persistentVolumeClaim:
          claimName: n8n-data-pvc

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: n8n-worker
  namespace: automation
spec:
  replicas: 2
  selector:
    matchLabels:
      app: n8n-worker
  template:
    metadata:
      labels:
        app: n8n-worker
    spec:
      securityContext:
        fsGroup: 1000
      containers:
      - name: n8n-worker
        image: n8nio/n8n:latest
        command: ["n8n", "worker"]
        securityContext:
          runAsUser: 1000
          runAsGroup: 1000
        envFrom:
        - configMapRef:
            name: n8n-config
        env:
        - name: N8N_ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: n8n-encryption-key
        - name: QUEUE_BULL_REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: redis-password
        - name: N8N_REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: redis-password
        - name: DB_POSTGRESDB_HOST
          value: "postgres-n8n-service"
        - name: DB_POSTGRESDB_PORT
          value: "5432"
        - name: DB_POSTGRESDB_DATABASE
          value: "n8n"
        - name: DB_POSTGRESDB_USER
          value: "n8n"
        - name: DB_POSTGRESDB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: n8n-db-password
        - name: DB_TYPE
          value: "postgresdb"
        volumeMounts:
        - name: n8n-data
          mountPath: /home/<USER>/.n8n
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: n8n-data
        persistentVolumeClaim:
          claimName: n8n-data-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: n8n-service
  namespace: automation
spec:
  selector:
    app: n8n
  ports:
  - port: 5678
    targetPort: 5678
  type: ClusterIP
