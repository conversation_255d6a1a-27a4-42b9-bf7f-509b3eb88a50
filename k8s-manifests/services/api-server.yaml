apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-server
  namespace: automation
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api-server
  template:
    metadata:
      labels:
        app: api-server
    spec:
      containers:
      - name: api-server
        image: gcr.io/macmini-home-server/api-server:latest
        envFrom:
        - configMapRef:
            name: api-server-config
        ports:
        - containerPort: 3001
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 10
          periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: api-server-service
  namespace: automation
spec:
  selector:
    app: api-server
  ports:
  - port: 3001
    targetPort: 3001
  type: ClusterIP
