apiVersion: apps/v1
kind: Deployment
metadata:
  name: langflow
  namespace: automation
spec:
  replicas: 1
  selector:
    matchLabels:
      app: langflow
  template:
    metadata:
      labels:
        app: langflow
    spec:
      containers:
      - name: langflow
        image: langflowai/langflow:latest
        envFrom:
        - configMapRef:
            name: langflow-config
        env:
        - name: LANGFLOW_DATABASE_URL
          value: "*************************************************************/langflow"
        ports:
        - containerPort: 7860
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 7860
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 7860
          initialDelaySeconds: 30
          periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: langflow-service
  namespace: automation
spec:
  selector:
    app: langflow
  ports:
  - port: 7860
    targetPort: 7860
  type: ClusterIP
