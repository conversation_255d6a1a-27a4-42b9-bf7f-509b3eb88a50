apiVersion: apps/v1
kind: Deployment
metadata:
  name: portainer
  namespace: automation
spec:
  replicas: 1
  selector:
    matchLabels:
      app: portainer
  template:
    metadata:
      labels:
        app: portainer
    spec:
      containers:
      - name: portainer
        image: portainer/portainer-ce:latest
        ports:
        - containerPort: 9000
        - containerPort: 8000
        volumeMounts:
        - name: portainer-data
          mountPath: /data
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 9000
          initialDelaySeconds: 10
          periodSeconds: 10
      volumes:
      - name: portainer-data
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: portainer-service
  namespace: automation
spec:
  selector:
    app: portainer
  ports:
  - name: web
    port: 9000
    targetPort: 9000
  - name: edge
    port: 8000
    targetPort: 8000
  type: ClusterIP
