networks:
  traefik-network:
    external: true

services:
  langflow:
    image: langflowai/langflow:latest
    # Remove direct port exposure as Traefik will handle this
    # ports:
    #   - "7860:7860"
    environment:
      - LANGFLOW_DATABASE_URL=********************************************/langflow
      - LANGFLOW_AUTO_LOGIN=false
      - LANGFLOW_SUPERUSER=admin
      - LANGFLOW_SUPERUSER_PASSWORD=randomPwd123
    depends_on:
      - postgres
    volumes:
      - langflow-data:/app/langflow
    networks:
      - traefik-network
      - default

  postgres:
    image: postgres:latest
    environment:
      - POSTGRES_USER=langflow
      - POSTGRES_PASSWORD=langflow
      - POSTGRES_DB=langflow
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - default

volumes:
  langflow-data:
  postgres-data: