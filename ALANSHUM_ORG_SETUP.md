# Google Cloud DNS Setup for alanshum.org

## Overview

This guide helps you set up Google Cloud DNS for your new domain `alanshum.org` registered at Squarespace, pointing to your GKE cluster.

## Architecture

```
subdomain.alanshum.org → Google Cloud DNS → GKE LoadBalancer (************)
```

**Services to be configured:**
- n8n.alanshum.org
- langflow.alanshum.org  
- api-server.alanshum.org
- portainer.alanshum.org
- grafana.alanshum.org
- prometheus.alanshum.org

---

## 🚀 Automated Setup (Recommended)

### Step 1: Run the Setup Script

```bash
# Make sure you're authenticated with gcloud
gcloud auth login

# Run the automated setup script
./scripts/setup-google-dns.sh
```

The script will:
1. ✅ Enable Cloud DNS API
2. ✅ Create DNS zone for `alanshum.org`
3. ✅ Create A records for all subdomains pointing to `************`
4. ✅ Display nameservers for Squarespace configuration

### Step 2: Update Squarespace Nameservers

1. **Log into Squarespace**
   - Go to your Squarespace account
   - Navigate to **Settings** → **Domains** → **alanshum.org**

2. **Switch to Custom Nameservers**
   - Click **"Use Custom Nameservers"**
   - Replace the current nameservers with the Google Cloud DNS nameservers shown by the script

3. **Save Changes**
   - Click **Save**
   - Wait 24-48 hours for DNS propagation

---

## 🔧 Manual Setup (Alternative)

### Step 1: Create DNS Zone

```bash
# Enable Cloud DNS API
gcloud services enable dns.googleapis.com --project=macmini-home-server

# Create DNS zone
gcloud dns managed-zones create alanshum-org-zone \
    --description="DNS zone for alanshum.org - GKE automation services" \
    --dns-name="alanshum.org." \
    --project=macmini-home-server
```

### Step 2: Create DNS Records

```bash
# Start transaction
gcloud dns record-sets transaction start \
    --zone=alanshum-org-zone \
    --project=macmini-home-server

# Add A records for each subdomain
gcloud dns record-sets transaction add ************ \
    --name="n8n.alanshum.org." \
    --ttl=300 \
    --type=A \
    --zone=alanshum-org-zone \
    --project=macmini-home-server

gcloud dns record-sets transaction add ************ \
    --name="langflow.alanshum.org." \
    --ttl=300 \
    --type=A \
    --zone=alanshum-org-zone \
    --project=macmini-home-server

gcloud dns record-sets transaction add ************ \
    --name="api-server.alanshum.org." \
    --ttl=300 \
    --type=A \
    --zone=alanshum-org-zone \
    --project=macmini-home-server

gcloud dns record-sets transaction add ************ \
    --name="portainer.alanshum.org." \
    --ttl=300 \
    --type=A \
    --zone=alanshum-org-zone \
    --project=macmini-home-server

gcloud dns record-sets transaction add ************ \
    --name="grafana.alanshum.org." \
    --ttl=300 \
    --type=A \
    --zone=alanshum-org-zone \
    --project=macmini-home-server

gcloud dns record-sets transaction add ************ \
    --name="prometheus.alanshum.org." \
    --ttl=300 \
    --type=A \
    --zone=alanshum-org-zone \
    --project=macmini-home-server

# Execute transaction
gcloud dns record-sets transaction execute \
    --zone=alanshum-org-zone \
    --project=macmini-home-server
```

### Step 3: Get Nameservers

```bash
# Get nameservers for Squarespace
gcloud dns managed-zones describe alanshum-org-zone \
    --project=macmini-home-server \
    --format="value(nameServers)"
```

---

## 🔄 Update Kubernetes Configuration

### Step 1: Apply Updated Ingress

```bash
# Apply the updated ingress configurations
kubectl apply -f k8s-manifests/ingress/main-ingress.yaml
kubectl apply -f k8s-manifests/ingress/monitoring-ingress.yaml
```

### Step 2: Verify SSL Certificates

```bash
# Check certificate status
kubectl get certificates -n automation
kubectl get certificates -n monitoring

# Describe certificates for details
kubectl describe certificate automation-tls -n automation
kubectl describe certificate monitoring-tls -n monitoring
```

---

## ✅ Verification (After DNS Propagation)

### Step 1: Check DNS Resolution

```bash
# Check DNS resolution (should return ************)
nslookup n8n.alanshum.org
nslookup langflow.alanshum.org
nslookup api-server.alanshum.org
nslookup portainer.alanshum.org
nslookup grafana.alanshum.org
nslookup prometheus.alanshum.org
```

### Step 2: Test Services

```bash
# Test service health endpoints
curl -I https://n8n.alanshum.org/healthz
curl -I https://api-server.alanshum.org/health
curl -I https://grafana.alanshum.org/api/health
curl -I https://prometheus.alanshum.org/-/healthy
```

### Step 3: Verify SSL Certificates

```bash
# Check SSL certificate details
openssl s_client -connect n8n.alanshum.org:443 -servername n8n.alanshum.org < /dev/null 2>/dev/null | openssl x509 -noout -text | grep -A 2 "Subject:"
```

---

## 📊 Benefits of Google Cloud DNS

### ✅ Advantages
- **Native SSL Support**: Let's Encrypt certificates work seamlessly
- **Better Performance**: Direct routing without tunnel overhead
- **Professional Setup**: Industry-standard DNS management
- **Cost Effective**: ~$0.50/month for DNS hosting
- **Reliability**: Google's global DNS infrastructure
- **Integration**: Native GCP integration with your existing setup

### 💰 Cost Comparison
- **Google Cloud DNS**: ~$0.50/month + $0.40 per million queries
- **Cloudflare Tunnel**: Free but with complexity and potential issues
- **Total Expected Cost**: < $1/month for typical usage

---

## 🔧 Troubleshooting

### DNS Not Resolving
```bash
# Check if DNS zone exists
gcloud dns managed-zones list --project=macmini-home-server

# Check DNS records
gcloud dns record-sets list --zone=alanshum-org-zone --project=macmini-home-server
```

### SSL Certificate Issues
```bash
# Check certificate events
kubectl describe certificate automation-tls -n automation

# Check cert-manager logs
kubectl logs -n cert-manager deployment/cert-manager
```

### Service Not Accessible
```bash
# Test LoadBalancer directly
curl -k -H "Host: n8n.alanshum.org" https://************/healthz

# Check ingress status
kubectl get ingress -n automation
kubectl describe ingress automation-ingress -n automation
```

---

## 📋 Next Steps

1. ✅ Run the setup script: `./scripts/setup-google-dns.sh`
2. ✅ Update Squarespace nameservers
3. ✅ Wait 24-48 hours for DNS propagation
4. ✅ Verify all services are accessible
5. ✅ Monitor SSL certificate issuance
6. ✅ Update any bookmarks or integrations to use new domain

**Timeline**: 
- Setup: 15 minutes
- DNS Propagation: 24-48 hours
- SSL Certificate Issuance: 5-10 minutes after DNS propagation
