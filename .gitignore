# n8n Backup System
# Ignore sensitive files but keep the backup structure
.env
*.env
.env.*

# Ignore temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Node modules
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
